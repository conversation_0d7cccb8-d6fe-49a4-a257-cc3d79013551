<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class PostSignatureRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $maxSize = config('attachment.upload.max_size', 10 * 1024 * 1024);
        $allowedTypes = config('attachment.upload.allowed_types', '');

        return [
            'filename' => 'required|string|max:255',
            'filesize' => 'required|integer|min:1|max:'.$maxSize,
            'mime_type' => 'required|string|max:100',
            'md5_hash' => 'nullable|string|size:32',
        ];
    }

    /**
     * 自定义错误消息
     */
    public function messages(): array
    {
        return [
            'filename.required' => '文件名不能为空',
            'filename.max' => '文件名过长',
            'filesize.required' => '文件大小不能为空',
            'filesize.max' => '文件大小超过限制',
            'mime_type.required' => 'MIME类型不能为空',
            'md5_hash.size' => 'MD5值格式不正确',
        ];
    }
}
