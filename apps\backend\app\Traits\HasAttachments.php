<?php

namespace App\Traits;

use App\Enums\AttachmentCategory;
use App\Models\Attachment;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

trait HasAttachments
{
    /**
     * 获取所有附件
     */
    public function attachments(): MorphToMany
    {
        return $this->morphToMany(Attachment::class, 'attachable', 'attachment_relations')
            ->withPivot(['category', 'sort', 'description'])
            ->withTimestamps()
            ->orderBy('attachment_relations.sort');
    }

    /**
     * 按分类获取附件
     */
    public function attachmentsByCategory(string $category): MorphToMany
    {
        return $this->attachments()->wherePivot('category', $category);
    }

    /**
     * 关联单个附件
     */
    public function attachFile(
        int $attachmentId,
        ?string $category = null,
        ?string $description = null,
        int $sort = 0
    ): void {
        $this->attachments()->attach($attachmentId, [
            'category' => $category ?? AttachmentCategory::GENERAL->value,
            'description' => $description,
            'sort' => $sort,
        ]);
    }

    /**
     * 批量关联附件
     */
    public function attachFiles(array $attachmentData): void
    {
        $data = [];
        foreach ($attachmentData as $index => $item) {
            $data[$item['id']] = [
                'category' => $item['category'] ?? AttachmentCategory::GENERAL->value,
                'description' => $item['description'] ?? null,
                'sort' => $item['sort'] ?? $index,
            ];
        }

        $this->attachments()->attach($data);
    }

    /**
     * 同步附件（替换现有附件）
     */
    public function syncAttachments(array $attachmentData): void
    {
        $data = [];
        foreach ($attachmentData as $index => $item) {
            $data[$item['id']] = [
                'category' => $item['category'] ?? AttachmentCategory::GENERAL->value,
                'description' => $item['description'] ?? null,
                'sort' => $item['sort'] ?? $index,
            ];
        }

        $this->attachments()->sync($data);
    }

    /**
     * 解除附件关联
     */
    public function detachFile(int $attachmentId): int
    {
        return $this->attachments()->detach($attachmentId);
    }

    /**
     * 解除所有附件关联
     */
    public function detachAllFiles(): int
    {
        return $this->attachments()->detach();
    }

    /**
     * 检查是否有某类附件
     */
    public function hasAttachmentCategory(string $category): bool
    {
        return $this->attachmentsByCategory($category)->exists();
    }

    /**
     * 获取某类附件的数量
     */
    public function attachmentCountByCategory(string $category): int
    {
        return $this->attachmentsByCategory($category)->count();
    }
}
