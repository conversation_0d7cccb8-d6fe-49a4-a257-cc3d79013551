<?php

namespace App\Services;

use App\Enums\AttachmentCategory;
use App\Models\Asset;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class AssetService
{
    /**
     * 分页获取资产列表
     */
    public function paginate(array $params = []): LengthAwarePaginator
    {
        $query = Asset::withCount(['attachments', 'children'])
            ->with(['parent']);

        // 名称搜索
        if (! empty($params['name'])) {
            $query->where('name', 'like', "%{$params['name']}%");
        }

        // 品牌搜索
        if (! empty($params['brand'])) {
            $query->where('brand', 'like', "%{$params['brand']}%");
        }

        // 序列号搜索
        if (! empty($params['serial_number'])) {
            $query->where('serial_number', 'like', "%{$params['serial_number']}%");
        }

        // 通用关键词搜索
        if (! empty($params['keyword'])) {
            $keyword = $params['keyword'];
            $query->where(function (Builder $q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                    ->orWhere('brand', 'like', "%{$keyword}%")
                    ->orWhere('model', 'like', "%{$keyword}%")
                    ->orWhere('serial_number', 'like', "%{$keyword}%");
            });
        }

        // 资产分类筛选
        if (! empty($params['asset_category_id'])) {
            $asset_category_id = $params['asset_category_id'];
            // 使用 JSON_CONTAINS 进行精确匹配
            $query->whereJsonContains('asset_category_ids', (int)$asset_category_id);
        }

        // 状态筛选
        if (! empty($params['asset_status'])) {
            $query->where('asset_status', $params['asset_status']);
        }

        // 成色筛选
        if (! empty($params['asset_condition'])) {
            $query->where('asset_condition', $params['asset_condition']);
        }

        // 资产来源筛选
        if (! empty($params['asset_source'])) {
            $query->where('asset_source', $params['asset_source']);
        }

        // 是否附属设备筛选（通过parent_id判断）
        if (isset($params['is_accessory'])) {
            if ($params['is_accessory']) {
                $query->whereNotNull('parent_id');
            } else {
                $query->whereNull('parent_id');
            }
        }

        // 主设备筛选
        if (! empty($params['parent_id'])) {
            $query->where('parent_id', $params['parent_id']);
        }

        // 排序
        $query->orderBy('created_at', 'desc');

        return $query->paginate($params['per_page'] ?? 20);
    }

    /**
     * 获取可作为主设备的资产列表（非附属设备）
     */
    public function getMainAssets(array $params = []): LengthAwarePaginator
    {
        $query = Asset::main()->active();

        // 排除指定资产（避免自己关联自己）
        if (! empty($params['exclude_id'])) {
            $query->where('id', '!=', $params['exclude_id']);
        }

        // 名称搜索
        if (! empty($params['keyword'])) {
            $keyword = $params['keyword'];
            $query->where(function (Builder $q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                    ->orWhere('brand', 'like', "%{$keyword}%")
                    ->orWhere('model', 'like', "%{$keyword}%");
            });
        }

        $query->orderBy('name');

        return $query->paginate($params['per_page'] ?? 20);
    }

    /**
     * 创建资产
     */
    public function create(array $data): Asset
    {
        return DB::transaction(function () use ($data) {
            // 验证父子关系
            if (! empty($data['parent_id'])) {
                $this->validateParentAsset($data['parent_id']);
            }

            // 创建资产
            $asset = Asset::create([
                'name' => $data['name'],
                'brand' => $data['brand'] ?? null,
                'model' => $data['model'] ?? null,
                'serial_number' => $data['serial_number'] ?? null,
                'asset_category_ids' => $data['asset_category_ids'] ?? null,
                'asset_source' => $data['asset_source'] ?? null,
                'asset_status' => $data['asset_status'] ?? null,
                'asset_condition' => $data['asset_condition'] ?? null,
                'parent_id' => $data['parent_id'] ?? null,
                'region_code' => $data['region_code'] ?? null,
                'detailed_address' => $data['detailed_address'] ?? null,
                'start_date' => $data['start_date'] ?? null,
                'warranty_period' => $data['warranty_period'] ?? null,
                'warranty_alert' => $data['warranty_alert'] ?? null,
                'maintenance_cycle' => $data['maintenance_cycle'] ?? null,
                'expected_years' => $data['expected_years'] ?? null,
                'related_entities' => $data['related_entities'] ?? null,
                'remark' => $data['remark'] ?? null,
                'created_by' => auth()->id(),
                'updated_by' => auth()->id(),
            ]);

            // 处理附件关联
            if (! empty($data['attachments'])) {
                $attachmentData = [];
                foreach ($data['attachments'] as $index => $attachmentId) {
                    $attachmentData[] = [
                        'id' => $attachmentId,
                        'category' => AttachmentCategory::GENERAL->value,
                        'sort' => $index,
                        'description' => null,
                    ];
                }
                $asset->attachFiles($attachmentData);
            }

            return $asset->load([
                'parent',
                'children',
                'attachments',
            ]);
        });
    }

    /**
     * 更新资产
     */
    public function update(Asset $asset, array $data): Asset
    {
        return DB::transaction(function () use ($asset, $data) {
            // 验证父子关系
            if (! empty($data['parent_id'])) {
                // 不能将自己设为父设备
                if ($data['parent_id'] == $asset->id) {
                    throw new \InvalidArgumentException('资产不能关联自己作为主设备');
                }
                $this->validateParentAsset($data['parent_id']);
            }

            // 更新资产信息
            $asset->update([
                'name' => $data['name'],
                'brand' => $data['brand'] ?? null,
                'model' => $data['model'] ?? null,
                'serial_number' => $data['serial_number'] ?? null,
                'asset_category_ids' => $data['asset_category_ids'] ?? null,
                'asset_source' => $data['asset_source'] ?? null,
                'asset_status' => $data['asset_status'] ?? null,
                'asset_condition' => $data['asset_condition'] ?? null,
                'parent_id' => $data['parent_id'] ?? null,
                'region_code' => $data['region_code'] ?? null,
                'detailed_address' => $data['detailed_address'] ?? null,
                'start_date' => $data['start_date'] ?? null,
                'warranty_period' => $data['warranty_period'] ?? null,
                'warranty_alert' => $data['warranty_alert'] ?? null,
                'maintenance_cycle' => $data['maintenance_cycle'] ?? null,
                'expected_years' => $data['expected_years'] ?? null,
                'related_entities' => $data['related_entities'] ?? null,
                'remark' => $data['remark'] ?? null,
                'updated_by' => auth()->id(),
            ]);

            // 处理附件更新
            if (isset($data['attachments'])) {
                $attachmentData = [];
                foreach ($data['attachments'] as $index => $attachmentId) {
                    $attachmentData[] = [
                        'id' => $attachmentId,
                        'category' => AttachmentCategory::GENERAL->value,
                        'sort' => $index,
                        'description' => null,
                    ];
                }
                $asset->syncAttachments($attachmentData);
            }

            return $asset->load([
                'parent',
                'children',
                'attachments',
            ]);
        });
    }

    /**
     * 删除资产
     */
    public function delete(Asset $asset): bool
    {
        return DB::transaction(function () use ($asset) {
            // 检查是否有附属设备
            if ($asset->hasChildren()) {
                throw new \InvalidArgumentException('该资产下还有附属设备，无法删除');
            }

            return $asset->delete();
        });
    }

    /**
     * 验证主设备是否有效
     */
    private function validateParentAsset(int $parentId): void
    {
        $parent = Asset::find($parentId);

        if (! $parent) {
            throw new \InvalidArgumentException('指定的主设备不存在');
        }

        if ($parent->parent_id !== null) {
            throw new \InvalidArgumentException('主设备不能是附属设备');
        }

        if ($parent->asset_status === 'scrap_registered') {
            throw new \InvalidArgumentException('主设备状态为报废，无法关联');
        }
    }
}
