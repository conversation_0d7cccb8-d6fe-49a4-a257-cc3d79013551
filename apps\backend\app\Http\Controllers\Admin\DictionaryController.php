<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\DictionaryCategoryRequest;
use App\Http\Requests\Admin\DictionaryItemRequest;
use App\Http\Resources\Admin\DictionaryCategoryResource;
use App\Http\Resources\Admin\DictionaryItemResource;
use App\Models\DictionaryCategory;
use App\Models\DictionaryItem;
use App\Services\DictionaryService;
use Illuminate\Http\Request;

/**
 * @group 字典管理
 *
 * 系统字典配置管理接口
 */
class DictionaryController extends Controller
{
    public function __construct(
        private DictionaryService $dictionaryService
    ) {}

    /**
     * 获取字典分类列表
     *
     * 获取所有字典分类，支持条件筛选
     *
     * @queryParam name string 分类名称 Example: 设备类型
     * @queryParam code string 分类编码 Example: device_type
     * @queryParam is_enabled boolean 是否启用 Example: true
     */
    public function categoryIndex(Request $request)
    {
        $filters = $request->only(['name', 'code', 'is_enabled']);
        $categories = $this->dictionaryService->getCategoryList($filters);

        return DictionaryCategoryResource::collection($categories);
    }

    /**
     * 创建字典分类
     *
     * 创建新的字典分类
     */
    public function categoryStore(DictionaryCategoryRequest $request)
    {
        $data = $request->validated();
        $data['created_by'] = $request->user()->id;

        $category = $this->dictionaryService->createCategory($data);

        return (new DictionaryCategoryResource($category))
            ->response()
            ->setStatusCode(201);
    }

    /**
     * 更新字典分类
     *
     * 更新指定的字典分类信息
     *
     * @urlParam category integer required 字典分类ID Example: 1
     */
    public function categoryUpdate(DictionaryCategoryRequest $request, DictionaryCategory $category)
    {
        $data = $request->validated();
        $data['updated_by'] = $request->user()->id;

        $category = $this->dictionaryService->updateCategory($category, $data);

        return new DictionaryCategoryResource($category);
    }

    /**
     * 删除字典分类
     *
     * 删除指定的字典分类（分类下存在字典项时无法删除）
     *
     * @urlParam category integer required 字典分类ID Example: 1
     */
    public function categoryDestroy(DictionaryCategory $category)
    {
        if ($category->items()->exists()) {
            return $this->error('该分类下存在字典项，无法删除', 422);
        }

        $this->dictionaryService->deleteCategory($category);

        return response()->json(null, 204);
    }

    /**
     * 获取字典项列表
     *
     * 获取所有字典项，支持条件筛选
     *
     * @queryParam category_id integer 分类ID Example: 1
     * @queryParam code string 字典编码 Example: desktop
     * @queryParam value string 字典值 Example: 台式机
     * @queryParam is_enabled boolean 是否启用 Example: true
     */
    public function itemIndex(Request $request)
    {
        $filters = $request->only(['category_id', 'code', 'value', 'is_enabled']);
        $items = $this->dictionaryService->getItemList($filters);

        return DictionaryItemResource::collection($items);
    }

    /**
     * 创建字典项
     *
     * 在指定分类下创建新的字典项
     */
    public function itemStore(DictionaryItemRequest $request)
    {
        $data = $request->validated();
        $data['created_by'] = $request->user()->id;

        $item = $this->dictionaryService->createItem($data);

        return (new DictionaryItemResource($item))
            ->response()
            ->setStatusCode(201);
    }

    /**
     * 更新字典项
     *
     * 更新指定的字典项信息
     *
     * @urlParam item integer required 字典项ID Example: 1
     */
    public function itemUpdate(DictionaryItemRequest $request, DictionaryItem $item)
    {
        $data = $request->validated();
        $data['updated_by'] = $request->user()->id;

        $item = $this->dictionaryService->updateItem($item, $data);

        return new DictionaryItemResource($item);
    }

    /**
     * 删除字典项
     *
     * 删除指定的字典项
     *
     * @urlParam item integer required 字典项ID Example: 1
     */
    public function itemDestroy(DictionaryItem $item)
    {
        $this->dictionaryService->deleteItem($item);

        return response()->json(null, 204);
    }

    /**
     * 根据分类编码获取字典项
     *
     * 获取指定分类编码下的所有启用的字典项
     *
     * @urlParam categoryCode string required 分类编码 Example: device_type
     */
    public function getByCode(string $categoryCode)
    {
        $items = $this->dictionaryService->getItemsByCode($categoryCode);

        return response()->json($items);
    }

    /**
     * 根据分类编码获取字典项（兼容旧API）
     *
     * @hideFromAPIDocumentation
     */
    public function getChildren(string $categoryCode)
    {
        return $this->getByCode($categoryCode);
    }
}
