<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class DictionaryCategory extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'code',
        'name',
        'description',
        'sort',
        'is_enabled',
        'created_by',
        'updated_by',
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'is_enabled' => 'boolean',
        'sort' => 'integer',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];

    /**
     * 隐藏的属性
     */
    protected $hidden = [
        'deleted_at',
    ];

    /**
     * 获取字典项
     */
    public function items(): HasMany
    {
        return $this->hasMany(DictionaryItem::class, 'category_id');
    }

    /**
     * 获取启用的字典项
     */
    public function enabledItems(): HasMany
    {
        return $this->items()->where('is_enabled', true)->orderBy('sort');
    }

    /**
     * 作用域：启用状态
     */
    public function scopeEnabled($query)
    {
        return $query->where('is_enabled', true);
    }

    /**
     * 作用域：按排序字段排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort')->orderBy('id');
    }
}
