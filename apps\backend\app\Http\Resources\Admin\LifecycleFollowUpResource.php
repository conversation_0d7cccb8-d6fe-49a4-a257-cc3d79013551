<?php

namespace App\Http\Resources\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LifecycleFollowUpResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'lifecycle_id' => $this->lifecycle_id,
            'date' => $this->date?->format('Y-m-d'),
            'person_id' => $this->person_id,
            'person_name' => $this->whenLoaded('person', fn () => $this->person->username),
            'content' => $this->content,
            'attachments' => $this->whenLoaded('attachments'),
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
        ];
    }
}
