<?php

namespace App\Services;

use App\Models\Category;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class CategoryService
{
    /**
     * 获取指定父级的子分类
     */
    public function getChildren(int $parentId): Collection
    {
        return Category::where('parent_id', $parentId)
            ->ordered()
            ->get();
    }

    /**
     * 创建分类
     */
    public function create(array $data): Category
    {
        return DB::transaction(function () use ($data) {
            // 如果有父级，自动计算层级
            if (! empty($data['parent_id'])) {
                $parent = Category::find($data['parent_id']);
                if (! $parent) {
                    throw new \Exception('父级分类不存在');
                }
                $data['level'] = $parent->level + 1;
            } else {
                $data['parent_id'] = 0;
                $data['level'] = 1;
            }

            return Category::create($data);
        });
    }

    /**
     * 更新分类
     */
    public function update(Category $category, array $data): Category
    {
        return DB::transaction(function () use ($category, $data) {
            // 如果修改了父级
            if (isset($data['parent_id']) && $data['parent_id'] != $category->parent_id) {
                // 不能将分类设置为自己的子分类
                if ($data['parent_id'] == $category->id) {
                    throw new \Exception('不能将分类设置为自己的子分类');
                }

                // 检查是否会形成循环引用
                if ($data['parent_id'] > 0) {
                    $parent = Category::find($data['parent_id']);
                    if (! $parent) {
                        throw new \Exception('父级分类不存在');
                    }

                    // 检查父级是否是当前分类的子分类
                    if ($this->isDescendant($category, $parent)) {
                        throw new \Exception('不能将分类移动到自己的子分类下');
                    }

                    $data['level'] = $parent->level + 1;
                } else {
                    $data['parent_id'] = 0;
                    $data['level'] = 1;
                }

                // 更新所有子分类的层级
                $this->updateChildrenLevel($category, $data['level']);
            }

            $category->update($data);

            return $category->fresh();
        });
    }

    /**
     * 删除分类
     */
    public function delete(Category $category): void
    {
        DB::transaction(function () use ($category) {
            if ($category->hasChildren()) {
                throw new \Exception('该分类下有子分类，无法删除');
            }

            $category->delete();
        });
    }

    /**
     * 检查是否是子孙分类
     */
    private function isDescendant(Category $category, Category $potentialParent): bool
    {
        $descendants = $category->descendants;
        foreach ($descendants as $descendant) {
            if ($descendant->id == $potentialParent->id) {
                return true;
            }
        }

        return false;
    }

    /**
     * 更新子分类层级
     */
    private function updateChildrenLevel(Category $category, int $parentLevel): void
    {
        $children = $category->children;
        foreach ($children as $child) {
            $child->update(['level' => $parentLevel + 1]);
            $this->updateChildrenLevel($child, $parentLevel + 1);
        }
    }
}
