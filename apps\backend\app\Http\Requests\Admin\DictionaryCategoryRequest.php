<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class DictionaryCategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $categoryId = $this->route('category')?->id;

        return [
            'code' => [
                'required',
                'string',
                'max:50',
                'regex:/^[a-zA-Z_][a-zA-Z0-9_]*$/',
                Rule::unique('dictionary_categories')->ignore($categoryId),
            ],
            'name' => 'required|string|max:100',
            'description' => 'nullable|string|max:500',
            'sort' => 'nullable|integer|min:0',
            'is_enabled' => 'boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'code.required' => '分类编码不能为空',
            'code.regex' => '分类编码只能包含字母、数字和下划线，且不能以数字开头',
            'code.unique' => '分类编码已存在',
            'name.required' => '分类名称不能为空',
            'name.max' => '分类名称不能超过100个字符',
            'description.max' => '分类描述不能超过500个字符',
            'sort.integer' => '排序必须是整数',
            'sort.min' => '排序不能小于0',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'code' => '分类编码',
            'name' => '分类名称',
            'description' => '分类描述',
            'sort' => '排序',
            'is_enabled' => '是否启用',
        ];
    }

    /**
     * Get the body parameters for Scribe documentation.
     */
    public function bodyParameters(): array
    {
        return [
            'code' => [
                'description' => '分类编码，只能包含字母、数字和下划线，且不能以数字开头',
                'example' => 'device_type',
            ],
            'name' => [
                'description' => '分类名称',
                'example' => '设备类型',
            ],
            'description' => [
                'description' => '分类描述',
                'example' => '设备类型分类',
            ],
            'sort' => [
                'description' => '排序值，数值越小越靠前',
                'example' => 1,
            ],
            'is_enabled' => [
                'description' => '是否启用',
                'example' => true,
            ],
        ];
    }
}
