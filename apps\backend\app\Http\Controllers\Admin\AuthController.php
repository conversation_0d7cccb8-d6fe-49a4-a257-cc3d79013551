<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\LoginRequest;
use App\Http\Resources\Admin\UserResource;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

/**
 * @group 授权
 *
 * 管理员认证相关接口
 */
class AuthController extends Controller
{
    /**
     * 登录
     *
     * 管理员登录接口，验证用户名和密码后返回访问令牌
     *
     * @unauthenticated
     *
     * @bodyParam username string required 用户名 Example: admin
     * @bodyParam password string required 密码 Example: password123
     */
    public function login(LoginRequest $request)
    {
        $admin = User::where('username', $request->username)->first();
        if (! Hash::check($request->password, $admin->password)) {
            throw ValidationException::withMessages([
                'username' => ['账号或密码错误'],
            ]);
        }

        // 加载用户的附件和角色关系
        $admin->load(['attachments', 'roles']);

        $token = $admin->createToken('admin-token')->plainTextToken;

        return response()->json([
            'token' => $token,
            'user' => new UserResource($admin),
        ]);
    }

    /**
     * 退出登录
     *
     * 撤销当前访问令牌，退出登录状态
     */
    public function logout(Request $request)
    {
        $request->user()->currentAccessToken()->delete();

        return response()->noContent();
    }
}
