<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Role extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
    ];

    // 默认加载权限关联
    protected $with = ['roleMenuPermissions', 'menus'];


    protected $appends = [];

    /**
     * 获取拥有此角色的用户
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_roles')
            ->withTimestamps();
    }

    /**
     * 获取角色的菜单权限关联记录
     */
    public function roleMenuPermissions(): HasMany
    {
        return $this->hasMany(RoleMenuPermission::class, 'role_id', 'id');
    }

    /**
     * 获取角色拥有的菜单（通过权限关联表）
     */
    public function menus(): BelongsToMany
    {
        return $this->belongsToMany(Menu::class, 'role_menu_permissions')
            ->withTimestamps()
            ->distinct();
    }

    /**
     * 获取角色拥有的菜单权限（通过权限关联表）
     */
    public function menuPermissions(): BelongsToMany
    {
        return $this->belongsToMany(MenuPermission::class, 'role_menu_permissions')
            ->withTimestamps()
            ->withPivot('menu_id');
    }

    /**
     * 检查角色是否有指定菜单的访问权限
     */
    public function hasMenuAccess(int $menuId): bool
    {
        return $this->roleMenuPermissions()
            ->where('menu_id', $menuId)
            ->exists();
    }

    /**
     * 检查角色是否有指定菜单的特定权限
     */
    public function hasMenuPermission(int $menuId, int $menuPermissionId): bool
    {
        return $this->roleMenuPermissions()
            ->where('menu_id', $menuId)
            ->where('menu_permission_id', $menuPermissionId)
            ->exists();
    }

    /**
     * 为角色分配菜单权限
     */
    public function assignMenuPermissions(array $permissions): void
    {
        foreach ($permissions as $permission) {
            $this->roleMenuPermissions()->updateOrCreate([
                'menu_id' => $permission['menu_id'],
                'menu_permission_id' => $permission['menu_permission_id'] ?? null,
            ]);
        }
    }

    /**
     * 同步角色的菜单权限（覆盖式）
     */
    public function syncMenuPermissions(array $permissions): void
    {
        // 删除现有权限
        $this->roleMenuPermissions()->delete();

        // 添加新权限
        $this->assignMenuPermissions($permissions);
    }
}
