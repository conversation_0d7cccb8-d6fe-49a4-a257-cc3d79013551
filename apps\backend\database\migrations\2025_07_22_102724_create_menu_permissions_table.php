<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('menu_permissions', function (Blueprint $table) {
            // 设置表备注
            $table->comment('菜单权限表');
            $table->id();
            $table->unsignedBigInteger('menu_id')->comment('菜单ID');
            $table->string('title', 50)->comment('权限名称');
            $table->string('auth_mark', 100)->comment('权限标识');
            $table->integer('sort')->default(0)->comment('排序');
            $table->timestamps();

            // 添加索引
            $table->index('menu_id');
            $table->unique(['menu_id', 'auth_mark']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('menu_permissions');
    }
};
