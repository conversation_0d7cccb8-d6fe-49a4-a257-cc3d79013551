<?php

namespace App\Models;

use App\Services\Attachment\StorageManager;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Attachment extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'file_name',
        'file_path',
        'file_size',
        'mime_type',
        'storage_type',
        'md5_hash',
    ];

    protected $casts = [
        'file_size' => 'integer',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];

    protected $appends = ['file_url'];

    /**
     * 获取文件访问URL
     */
    public function getFileUrlAttribute(): string
    {
        if (! $this->file_path) {
            return '';
        }

        try {
            $storageManager = app(StorageManager::class);
            $driver = $storageManager->driver($this->storage_type);

            return $driver->url($this->file_path);
        } catch (\Exception $e) {
            // 如果驱动不存在，返回原始路径
            return $this->file_path;
        }
    }

    /**
     * 获取格式化的文件大小
     */
    public function getFormattedFileSizeAttribute(): string
    {
        $bytes = $this->file_size;

        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2).' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2).' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2).' KB';
        } else {
            return $bytes.' bytes';
        }
    }

    /**
     * 获取所有拥有此附件的主体
     */
    public function entities(): MorphToMany
    {
        return $this->morphedByMany(Entity::class, 'attachable', 'attachment_relations')
            ->withPivot(['category', 'sort', 'description'])
            ->withTimestamps();
    }

    /**
     * 获取附件的所有关联
     */
    public function attachables()
    {
        return $this->hasMany(AttachmentRelation::class);
    }

    /**
     * 获取创建人
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }
}
