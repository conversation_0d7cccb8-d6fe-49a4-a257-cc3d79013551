<?php

namespace App\Models;

use App\Traits\HasAttachments;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Entity extends Model
{
    use HasAttachments, HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'tax_number',
        'entity_type',
        'address',
        'phone',
        'keywords',
        'remark',
        'created_by',
        'updated_by',
    ];

    protected $hidden = ['deleted_at'];

    protected $casts = [
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];

    public function contacts(): HasMany
    {
        return $this->hasMany(EntityContact::class);
    }
}
