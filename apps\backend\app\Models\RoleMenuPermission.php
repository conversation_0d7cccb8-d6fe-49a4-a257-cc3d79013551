<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RoleMenuPermission extends Model
{
    protected $fillable = [
        'role_id',
        'menu_id', 
        'menu_permission_id',
    ];

    /**
     * 关联角色
     */
    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class);
    }

    /**
     * 关联菜单
     */
    public function menu(): BelongsTo
    {
        return $this->belongsTo(Menu::class);
    }

    /**
     * 关联菜单权限
     */
    public function menuPermission(): BelongsTo
    {
        return $this->belongsTo(MenuPermission::class);
    }
}
