<?php

namespace App\Services\Attachment\Contracts;

use Illuminate\Http\UploadedFile;

interface StorageDriver
{
    /**
     * 本地上传文件
     */
    public function store(UploadedFile $file, string $path): string;

    /**
     * 获取PostObject签名（仅OSS需要）
     */
    public function getPostSignature(string $filename, int $filesize, string $mimeType, ?string $md5 = null): array;

    /**
     * 验证回调
     */
    public function validateCallback(array $data): bool;

    /**
     * 删除文件
     */
    public function delete(string $path): bool;

    /**
     * 获取访问URL
     */
    public function url(string $path): string;

    /**
     * 判断文件是否存在
     */
    public function exists(string $path): bool;

    /**
     * 获取存储类型标识
     */
    public function getType(): string;
}
