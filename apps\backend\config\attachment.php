<?php

return [
    // 默认存储驱动
    'default' => env('ATTACHMENT_DRIVER', 'local'),

    // 通用配置
    'upload' => [
        'max_size' => env('ATTACHMENT_MAX_SIZE', 10 * 1024 * 1024), // 10MB
        'allowed_types' => env('ATTACHMENT_ALLOWED_TYPES', 'jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,ppt,pptx,txt,zip,rar'),
        'chunk_size' => 2 * 1024 * 1024, // MD5计算分块大小
    ],

    // 存储驱动配置
    'drivers' => [
        'local' => [
            'disk' => 'public',
            'path' => 'attachments',
        ],

        'alioss' => [
            'access_key_id' => env('ALIOSS_ACCESS_KEY_ID'),
            'access_key_secret' => env('ALIOSS_ACCESS_KEY_SECRET'),
            'bucket' => env('ALIOSS_BUCKET'),
            'endpoint' => env('ALIOSS_ENDPOINT', 'oss-cn-hangzhou.aliyuncs.com'),
            'is_cname' => env('ALIOSS_IS_CNAME', false),
            'use_ssl' => env('ALIOSS_USE_SSL', true),
            'prefix' => env('ALIOSS_PREFIX', 'attachments'),
            'policy_expire' => env('ALIOSS_POLICY_EXPIRE', 3600), // 签名有效期（秒）
        ],
    ],

    // 秒传配置
    'quick_upload' => [
        'enabled' => env('ATTACHMENT_QUICK_UPLOAD', true),
        'cache_ttl' => 7200, // 缓存时间2小时
    ],
];
