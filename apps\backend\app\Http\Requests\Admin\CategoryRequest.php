<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $categoryId = $this->route('category')?->id;

        return [
            'name' => ['required', 'string', 'max:100'],
            'code' => [
                'required',
                'string',
                'max:50',
                Rule::unique('categories')->ignore($categoryId),
            ],
            'parent_id' => ['integer', 'min:0'],
            'sort' => ['integer', 'min:0'],
            'status' => ['integer', 'in:0,1'],
            'remark' => ['nullable', 'string', 'max:500'],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'name' => '分类名称',
            'code' => '分类编码',
            'parent_id' => '父级ID',
            'sort' => '排序值',
            'status' => '状态',
            'remark' => '备注',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'parent_id' => $this->parent_id ?? 0,
            'sort' => $this->sort ?? 0,
            'status' => $this->status ?? 1,
        ]);
    }
}
