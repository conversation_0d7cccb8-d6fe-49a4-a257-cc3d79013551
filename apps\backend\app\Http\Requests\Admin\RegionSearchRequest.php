<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class RegionSearchRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'keyword' => 'required|string|min:1',
            'limit' => 'integer|min:1|max:50',
            'deep' => 'integer|in:0,1,2',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'keyword.required' => '搜索关键词不能为空',
            'keyword.min' => '搜索关键词至少1个字符',
            'limit.integer' => '返回数量必须是整数',
            'limit.min' => '返回数量最少为1',
            'limit.max' => '返回数量最多为50',
            'deep.integer' => '层级必须是整数',
            'deep.in' => '层级必须是0(省)、1(市)或2(区县)',
        ];
    }
}
