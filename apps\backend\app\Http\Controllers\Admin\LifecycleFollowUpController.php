<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\LifecycleFollowUpRequest;
use App\Http\Resources\Admin\LifecycleFollowUpResource;
use App\Models\Lifecycle;
use App\Services\LifecycleFollowUpService;

/**
 * @group 生命周期管理
 *
 * 管理生命周期跟进记录
 */
class LifecycleFollowUpController extends Controller
{
    public function __construct(
        private LifecycleFollowUpService $followUpService
    ) {}

    /**
     * 获取生命周期的跟进记录列表
     *
     * @urlParam lifecycleId integer required 生命周期ID. Example: 1
     */
    public function index(int $lifecycleId)
    {
        $lifecycle = Lifecycle::find($lifecycleId);

        if (! $lifecycle) {
            return $this->error('生命周期记录不存在', 404);
        }

        $followUps = $this->followUpService->getByLifecycle($lifecycleId);

        return LifecycleFollowUpResource::collection($followUps);
    }

    /**
     * 获取跟进记录详情
     *
     * @urlParam lifecycleId integer required 生命周期ID. Example: 1
     * @urlParam id integer required 跟进记录ID. Example: 1
     */
    public function show(int $lifecycleId, int $id)
    {
        $followUp = $this->followUpService->find($lifecycleId, $id);

        if (! $followUp) {
            return $this->error('跟进记录不存在', 404);
        }

        return new LifecycleFollowUpResource($followUp);
    }

    /**
     * 创建跟进记录
     *
     * @urlParam lifecycleId integer required 生命周期ID. Example: 1
     *
     * @bodyParam date string required 日期. Example: 2024-01-16
     * @bodyParam person_id integer required 跟进人ID（必须是协助人员之一）. Example: 2
     * @bodyParam content string required 内容. Example: 已联系供应商确认发货时间
     * @bodyParam attachments integer[] 附件ID数组. Example: [1, 2, 3]
     */
    public function store(LifecycleFollowUpRequest $request, int $lifecycleId)
    {
        $lifecycle = Lifecycle::find($lifecycleId);

        if (! $lifecycle) {
            return $this->error('生命周期记录不存在', 404);
        }

        $followUp = $this->followUpService->create($lifecycle, $request->validated());

        return (new LifecycleFollowUpResource($followUp))->response()->setStatusCode(201);
    }

    /**
     * 更新跟进记录
     *
     * @urlParam lifecycleId integer required 生命周期ID. Example: 1
     * @urlParam id integer required 跟进记录ID. Example: 1
     *
     * @bodyParam date string 日期. Example: 2024-01-16
     * @bodyParam person_id integer 跟进人ID（必须是协助人员之一）. Example: 2
     * @bodyParam content string 内容. Example: 已联系供应商确认发货时间
     * @bodyParam attachments integer[] 附件ID数组. Example: [1, 2, 3]
     */
    public function update(LifecycleFollowUpRequest $request, int $lifecycleId, int $id)
    {
        $followUp = $this->followUpService->validateBelongsToLifecycle($lifecycleId, $id);

        if (! $followUp) {
            return $this->error('跟进记录不存在', 404);
        }

        $followUp = $this->followUpService->update($followUp, $request->validated());

        return new LifecycleFollowUpResource($followUp);
    }

    /**
     * 删除跟进记录
     *
     * @urlParam lifecycleId integer required 生命周期ID. Example: 1
     * @urlParam id integer required 跟进记录ID. Example: 1
     */
    public function destroy(int $lifecycleId, int $id)
    {
        $followUp = $this->followUpService->validateBelongsToLifecycle($lifecycleId, $id);

        if (! $followUp) {
            return $this->error('跟进记录不存在', 404);
        }

        $this->followUpService->delete($followUp);

        return response()->noContent();
    }
}
