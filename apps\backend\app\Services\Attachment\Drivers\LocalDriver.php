<?php

namespace App\Services\Attachment\Drivers;

use App\Services\Attachment\Contracts\StorageDriver;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class LocalDriver implements StorageDriver
{
    protected array $config;

    public function __construct(array $config)
    {
        $this->config = $config;
    }

    /**
     * 本地上传文件
     */
    public function store(UploadedFile $file, string $path): string
    {
        $disk = $this->config['disk'] ?? 'public';

        // 使用 storeAs 来指定完整路径
        return $file->storeAs(dirname($path), basename($path), $disk);
    }

    /**
     * 获取PostObject签名（本地存储不需要）
     */
    public function getPostSignature(string $filename, int $filesize, string $mimeType, ?string $md5 = null): array
    {
        throw new \BadMethodCallException('Local storage does not support direct upload.');
    }

    /**
     * 验证回调（本地存储不需要）
     */
    public function validateCallback(array $data): bool
    {
        return true;
    }

    /**
     * 删除文件
     */
    public function delete(string $path): bool
    {
        $disk = $this->config['disk'] ?? 'public';

        return Storage::disk($disk)->delete($path);
    }

    /**
     * 获取访问URL
     */
    public function url(string $path): string
    {
        $disk = $this->config['disk'] ?? 'public';

        return Storage::disk($disk)->url($path);
    }

    /**
     * 判断文件是否存在
     */
    public function exists(string $path): bool
    {
        $disk = $this->config['disk'] ?? 'public';

        return Storage::disk($disk)->exists($path);
    }

    /**
     * 获取存储类型标识
     */
    public function getType(): string
    {
        return 'local';
    }
}
