<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 创建附件表
        Schema::create('attachments', function (Blueprint $table) {
            $table->id();
            $table->string('file_name')->comment('原始文件名');
            $table->string('file_path', 500)->comment('存储路径');
            $table->unsignedBigInteger('file_size')->comment('文件大小(字节)');
            $table->string('mime_type', 100)->comment('MIME类型');
            $table->string('storage_type', 20)->default('local')->comment('存储类型:local/alioss/qiniu/aws');
            $table->char('md5_hash', 32)->nullable()->comment('MD5哈希值');
            $table->timestamps();
            $table->softDeletes();

            // 索引
            $table->index('md5_hash');
            $table->index('storage_type');
            $table->index('created_at');

            $table->comment('附件表');
        });

        // 创建附件关联表
        Schema::create('attachment_relations', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('attachment_id')->comment('附件ID');
            $table->unsignedBigInteger('attachable_id')->comment('业务表主键');
            $table->string('attachable_type', 50)->comment('业务表类型');
            $table->string('category', 50)->nullable()->comment('附件分类');
            $table->unsignedInteger('sort')->default(0)->comment('排序');
            $table->string('description', 500)->nullable()->comment('附件描述');
            $table->timestamps();

            // 索引
            $table->index('attachment_id');
            $table->index(['attachable_id', 'attachable_type']);
            $table->index('category');

            // 外键约束
            $table->foreign('attachment_id')->references('id')->on('attachments')->onDelete('cascade');

            $table->comment('附件关联表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('attachment_relations');
        Schema::dropIfExists('attachments');
    }
};
