<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Menu extends Model
{
    protected $fillable = [
        'parent_id',
        'name',
        'path',
        'component',
        'title',
        'icon',
        'label',
        'sort',
        'is_hide',
        'is_hide_tab',
        'link',
        'is_iframe',
        'keep_alive',
        'is_first_level',
        'fixed_tab',
        'active_path',
        'is_full_page',
        'show_badge',
        'show_text_badge',
        'status',
    ];

    protected $casts = [
        'is_hide' => 'boolean',
        'is_hide_tab' => 'boolean',
        'is_iframe' => 'boolean',
        'keep_alive' => 'boolean',
        'is_first_level' => 'boolean',
        'fixed_tab' => 'boolean',
        'is_full_page' => 'boolean',
        'show_badge' => 'boolean',
        'status' => 'boolean',
    ];

    // 默认加载权限关联
    protected $with = ['permissions'];

    // 追加 meta 属性
    protected $appends = ['meta'];

    /**
     * 关联权限
     */
    public function permissions(): HasMany
    {
        return $this->hasMany(MenuPermission::class)->orderBy('sort', 'desc');
    }

    /**
     * 获取meta数据
     */
    public function getMetaAttribute(): array
    {
        return [
            'title' => $this->title,
            'icon' => $this->icon,
            'keepAlive' => $this->keep_alive,
            'showBadge' => $this->show_badge,
            'showTextBadge' => $this->show_text_badge,
            'isHide' => $this->is_hide,
            'isHideTab' => $this->is_hide_tab,
            'link' => $this->link,
            'isIframe' => $this->is_iframe,
            'authList' => $this->permissions->map(function ($permission) {
                return [
                    'title' => $permission->title,
                    'authMark' => $permission->auth_mark,
                ];
            })->toArray(),
            'isFirstLevel' => $this->is_first_level,
            'fixedTab' => $this->fixed_tab,
            'activePath' => $this->active_path,
            'isFullPage' => $this->is_full_page,
        ];
    }
}
