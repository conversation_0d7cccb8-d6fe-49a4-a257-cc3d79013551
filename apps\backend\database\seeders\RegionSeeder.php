<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RegionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('开始导入地区数据...');

        // 清空表
        DB::table('regions')->truncate();

        // 读取CSV文件
        $csvPath = storage_path('app/data/regions/ok_data_level3.csv');

        if (! file_exists($csvPath)) {
            $this->command->error('CSV文件不存在：'.$csvPath);

            return;
        }

        // 打开CSV文件
        $file = fopen($csvPath, 'r');
        if (! $file) {
            $this->command->error('无法打开CSV文件');

            return;
        }

        // 跳过标题行
        fgetcsv($file);

        $data = [];
        $count = 0;

        while (($row = fgetcsv($file)) !== false) {
            // 移除BOM字符并处理编码
            $row = array_map(function ($value) {
                return trim(str_replace("\xEF\xBB\xBF", '', $value), '"');
            }, $row);

            // 跳过空行
            if (empty(array_filter($row))) {
                continue;
            }

            // 确保有足够的列
            if (count($row) < 8) {
                continue;
            }

            $data[] = [
                'id' => (int) $row[0],
                'pid' => (int) $row[1],
                'deep' => (int) $row[2],
                'name' => $row[3],
                'pinyin_prefix' => $row[4],
                'pinyin' => $row[5],
                'ext_id' => $row[6],
                'ext_name' => $row[7],
                'created_at' => now(),
                'updated_at' => now(),
            ];

            $count++;

            // 每1000条插入一次
            if (count($data) >= 1000) {
                try {
                    // 使用insertOrIgnore忽略重复数据
                    DB::table('regions')->insertOrIgnore($data);
                    $this->command->info("已导入 {$count} 条记录...");
                    $data = [];
                } catch (\Exception $e) {
                    $this->command->error('插入数据失败：'.$e->getMessage());
                    Log::error('Region import error', [
                        'error' => $e->getMessage(),
                        'count' => $count,
                    ]);
                    break;
                }
            }
        }

        // 插入剩余数据
        if (! empty($data)) {
            try {
                DB::table('regions')->insertOrIgnore($data);
            } catch (\Exception $e) {
                $this->command->error('插入剩余数据失败：'.$e->getMessage());
                Log::error('Region import final error', [
                    'error' => $e->getMessage(),
                    'remaining_count' => count($data),
                ]);
            }
        }

        fclose($file);

        $totalCount = DB::table('regions')->count();
        $this->command->info("地区数据导入完成！共导入 {$totalCount} 条记录。");
    }
}
