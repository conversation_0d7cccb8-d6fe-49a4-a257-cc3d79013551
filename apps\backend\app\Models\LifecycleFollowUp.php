<?php

namespace App\Models;

use App\Traits\HasAttachments;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LifecycleFollowUp extends Model
{
    use HasAttachments, HasFactory;

    protected $fillable = [
        'lifecycle_id',
        'date',
        'person_id',
        'content',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'date' => 'date',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];

    /**
     * 获取所属的生命周期
     */
    public function lifecycle(): BelongsTo
    {
        return $this->belongsTo(Lifecycle::class);
    }

    /**
     * 获取跟进人
     */
    public function person(): BelongsTo
    {
        return $this->belongsTo(User::class, 'person_id');
    }

    /**
     * 获取创建人
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 获取更新人
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
}
