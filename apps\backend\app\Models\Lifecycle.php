<?php

namespace App\Models;

use App\Traits\HasAttachments;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Lifecycle extends Model
{
    use HasAttachments, HasFactory, SoftDeletes;

    protected $fillable = [
        'asset_id',
        'type',
        'date',
        'initiator_id',
        'content',
        'acceptance_entity_id',
        'acceptance_personnel_id',
        'acceptance_time',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'date' => 'date',
        'acceptance_time' => 'datetime',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];

    protected $hidden = [
        'deleted_at',
    ];

    /**
     * 获取关联的资产
     */
    public function asset(): BelongsTo
    {
        return $this->belongsTo(Asset::class, 'asset_id');
    }

    /**
     * 获取发起人
     */
    public function initiator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'initiator_id');
    }

    /**
     * 获取协助人员
     */
    public function assistants(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'lifecycle_assistants', 'lifecycle_id', 'user_id')
            ->withTimestamps();
    }

    /**
     * 获取验收主体
     */
    public function acceptanceEntity(): BelongsTo
    {
        return $this->belongsTo(Entity::class, 'acceptance_entity_id');
    }

    /**
     * 获取验收人员
     */
    public function acceptancePersonnel(): BelongsTo
    {
        return $this->belongsTo(EntityContact::class, 'acceptance_personnel_id');
    }

    /**
     * 获取跟进记录
     */
    public function followUps(): HasMany
    {
        return $this->hasMany(LifecycleFollowUp::class);
    }

    /**
     * 获取创建人
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 获取更新人
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * 检查用户是否是协助人员
     */
    public function isAssistant(int $userId): bool
    {
        return $this->assistants()->where('user_id', $userId)->exists();
    }

    /**
     * 同步协助人员
     */
    public function syncAssistants(array $userIds): void
    {
        $this->assistants()->sync($userIds);
    }
}
