<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Seeder;

class AssetCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 行业分类
        $industryCategories = [
            [
                'name' => '行业分类',
                'code' => 'industry_category',
                'parent_id' => 0,
                'level' => 1,
                'sort' => 1,
                'children' => [],
            ],
        ];

        // 科室分类
        $departmentCategories = [
            [
                'name' => '科室',
                'code' => 'department',
                'parent_id' => 0,
                'level' => 1,
                'sort' => 2,
                'children' => [
                    ['name' => '手术科', 'code' => 'surgery_department'],
                    ['name' => '儿科门诊', 'code' => 'pediatric_outpatient'],
                    ['name' => '口鼻喉', 'code' => 'ent_department'],
                ],
            ],
        ];

        // 医疗分类
        $medicalCategories = [
            [
                'name' => '医疗分类',
                'code' => 'medical_category',
                'parent_id' => 0,
                'level' => 1,
                'sort' => 3,
                'children' => [],
            ],
        ];

        $this->createCategories('asset', $industryCategories);
        $this->createCategories('asset', $departmentCategories);
        $this->createCategories('asset', $medicalCategories);
    }

    /**
     * 创建分类数据
     */
    private function createCategories(string $type, array $categories, int $parentId = 0, int $level = 1): void
    {
        foreach ($categories as $categoryData) {
            $category = Category::create([
                'name' => $categoryData['name'],
                'code' => $type.'_'.$categoryData['code'],
                'parent_id' => $parentId,
                'level' => $level,
                'sort' => $categoryData['sort'] ?? 0,
                'status' => 1,
                'remark' => $type.'分类',
            ]);

            // 创建子分类
            if (! empty($categoryData['children'])) {
                foreach ($categoryData['children'] as $index => $child) {
                    Category::create([
                        'name' => $child['name'],
                        'code' => $type.'_'.$child['code'],
                        'parent_id' => $category->id,
                        'level' => $level + 1,
                        'sort' => $index + 1,
                        'status' => 1,
                        'remark' => $type.'分类',
                    ]);
                }
            }
        }
    }
}
