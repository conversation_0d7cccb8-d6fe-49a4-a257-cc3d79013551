<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class EntityRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $entityId = $this->route('entity')?->id;

        return [
            'name' => 'required|string|max:100',
            'tax_number' => [
                'nullable',
                'string',
                'max:50',
                Rule::unique('entities')->ignore($entityId),
            ],
            'entity_type' => 'required|string|exists:dictionary_items,code,category_id,6',
            'address' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:20',
            'keywords' => 'nullable|string|max:50',
            'remark' => 'nullable|string',
            'contacts' => 'nullable|array',
            'contacts.*.id' => 'nullable|integer|exists:entity_contacts,id',
            'contacts.*.name' => 'required|string|max:50',
            'contacts.*.phone' => 'required|string|max:20',
            'contacts.*.position' => 'nullable|string|max:50',
            'contacts.*.department' => 'nullable|string|max:50',
            'attachments' => 'nullable|array',
            'attachments.*' => 'integer|exists:attachments,id',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'name' => '主体名称',
            'tax_number' => '税号',
            'entity_type' => '主体类型',
            'address' => '地址',
            'phone' => '联系电话',
            'keywords' => '特征词',
            'remark' => '备注',
            'contacts' => '联系人',
            'contacts.*.name' => '联系人姓名',
            'contacts.*.phone' => '联系人电话',
            'contacts.*.position' => '职位',
            'contacts.*.department' => '部门',
            'attachments' => '附件',
            'attachments.*' => '附件ID',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // 前端已统一使用 snake_case，不需要转换
    }
}
