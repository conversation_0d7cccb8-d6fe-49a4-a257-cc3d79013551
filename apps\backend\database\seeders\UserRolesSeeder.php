<?php

namespace Database\Seeders;

use App\Models\UserRole;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class UserRolesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 清空表数据 - 先禁用外键检查
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        UserRole::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        UserRole::firstOrCreate(
            ['user_id' => 1],
            ['role_id' => 1]
        );
    }
}
