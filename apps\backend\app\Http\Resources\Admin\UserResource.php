<?php

namespace App\Http\Resources\Admin;

use App\Enums\AttachmentCategory;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        // 获取用户头像附件 - 从已加载的关系中筛选
        $avatar = null;
        if ($this->relationLoaded('attachments')) {
            $avatar = $this->attachments->filter(function ($attachment) {
                return $attachment->pivot->category === AttachmentCategory::AVATAR->value;
            })->first();
        }

        return [
            'id' => $this->id,
            'tenant_id' => $this->tenant_id,
            'username' => $this->username,
            'email' => $this->email,
            'phone' => $this->phone,
            'avatar' => $avatar ? $avatar->file_url : null,
            'avatar_id' => $avatar?->id,
            'status' => $this->status,
            'status_label' => $this->status === 'enable' ? '启用' : '禁用',
            'roles' => $this->when($this->relationLoaded('roles'), function () {
                return $this->roles->map(function ($role) {
                    return [
                        'id' => $role->id,
                        'name' => $role->name,
                        'description' => $role->description,
                    ];
                });
            }),
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
        ];
    }
}
