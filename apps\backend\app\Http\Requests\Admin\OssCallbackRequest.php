<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class OssCallbackRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'upload_id' => 'required|uuid',
            'object_key' => 'required|string|max:500',
        ];
    }

    /**
     * 自定义错误消息
     */
    public function messages(): array
    {
        return [
            'upload_id.required' => '上传ID不能为空',
            'upload_id.uuid' => '上传ID格式不正确',
            'object_key.required' => '文件路径不能为空',
            'object_key.max' => '文件路径过长',
        ];
    }
}
