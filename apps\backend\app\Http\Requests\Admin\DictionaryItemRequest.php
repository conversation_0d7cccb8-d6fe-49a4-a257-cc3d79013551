<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class DictionaryItemRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $itemId = $this->route('item')?->id;
        $categoryId = $this->input('category_id', $this->route('item')?->category_id);

        return [
            'category_id' => 'required|exists:dictionary_categories,id',
            'code' => [
                'required',
                'string',
                'max:50',
                Rule::unique('dictionary_items')
                    ->where('category_id', $categoryId)
                    ->ignore($itemId),
            ],
            'value' => 'required|string|max:200',
            'label' => 'nullable|string|max:200',
            'sort' => 'nullable|integer|min:0',
            'color' => 'nullable|string|max:50',
            'icon' => 'nullable|string|max:50',
            'config' => 'nullable|array',
            'remark' => 'nullable|string|max:500',
            'is_enabled' => 'boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'category_id.required' => '字典分类不能为空',
            'category_id.exists' => '字典分类不存在',
            'code.required' => '字典编码不能为空',
            'code.unique' => '该分类下字典编码已存在',
            'value.required' => '字典值不能为空',
            'value.max' => '字典值不能超过200个字符',
            'label.max' => '显示标签不能超过200个字符',
            'sort.integer' => '排序必须是整数',
            'sort.min' => '排序不能小于0',
            'color.regex' => '颜色格式不正确，应为#开头的6位16进制颜色值',
            'remark.max' => '备注不能超过500个字符',
            'config.array' => '扩展配置必须是数组格式',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'category_id' => '字典分类',
            'code' => '字典编码',
            'value' => '字典值',
            'label' => '显示标签',
            'sort' => '排序',
            'color' => '颜色',
            'icon' => '图标',
            'config' => '扩展配置',
            'remark' => '备注',
            'is_enabled' => '是否启用',
        ];
    }

    /**
     * Get the body parameters for Scribe documentation.
     */
    public function bodyParameters(): array
    {
        return [
            'category_id' => [
                'description' => '所属字典分类ID',
                'example' => 1,
            ],
            'code' => [
                'description' => '字典编码，同一分类下唯一',
                'example' => 'desktop',
            ],
            'value' => [
                'description' => '字典值，用于显示',
                'example' => '台式机',
            ],
            'label' => [
                'description' => '显示标签，可选的备用显示文本',
                'example' => '台式计算机',
            ],
            'sort' => [
                'description' => '排序值，数值越小越靠前',
                'example' => 1,
            ],
            'color' => [
                'description' => '颜色值，格式为#开头的6位16进制',
                'example' => '#FF5733',
            ],
            'icon' => [
                'description' => '图标标识',
                'example' => 'el-icon-monitor',
            ],
            'config' => [
                'description' => '扩展配置，JSON格式',
                'example' => ['key' => 'value'],
            ],
            'remark' => [
                'description' => '备注说明',
                'example' => '用于标识台式计算机类型',
            ],
            'is_enabled' => [
                'description' => '是否启用',
                'example' => true,
            ],
        ];
    }
}
