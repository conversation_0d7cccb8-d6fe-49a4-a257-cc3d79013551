<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('categories', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100)->comment('分类名称');
            $table->string('code', 50)->unique()->comment('分类编码');
            $table->unsignedBigInteger('parent_id')->default(0)->comment('父级ID，0表示顶级');
            $table->unsignedTinyInteger('level')->default(1)->comment('层级深度');
            $table->unsignedInteger('sort')->default(0)->comment('排序');
            $table->unsignedTinyInteger('status')->default(1)->comment('状态：0-禁用，1-启用');
            $table->string('remark', 500)->nullable()->comment('备注');
            $table->timestamps();

            $table->index('parent_id');
            $table->index('level');
            $table->index('sort');
            $table->index('status');

            $table->comment('无限级分类表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('categories');
    }
};
