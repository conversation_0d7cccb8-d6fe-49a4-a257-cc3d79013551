<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\UserRoleAssignRequest;
use App\Http\Requests\Admin\UserRoleRemoveRequest;
use App\Http\Requests\Admin\UserRoleSyncRequest;
use App\Http\Resources\Admin\UserResource;
use App\Models\User;
use App\Services\UserService;

/**
 * @group 用户管理
 */
class UserRoleController extends Controller
{
    public function __construct(
        private UserService $userService
    ) {}

    /**
     * 为用户分配角色（追加式）
     *
     * @urlParam user integer required 用户ID. Example: 1
     *
     * @bodyParam role_ids array required 角色ID数组. Example: [1, 2]
     *
     * @response {
     *   "id": 1,
     *   "name": "张三",
     *   "email": "<EMAIL>",
     *   "roles": [
     *     {
     *       "id": 1,
     *       "name": "管理员",
     *       "description": "系统管理员角色"
     *     },
     *     {
     *       "id": 2,
     *       "name": "普通用户",
     *       "description": "普通用户角色"
     *     }
     *   ]
     * }
     */
    public function assign(UserRoleAssignRequest $request, User $user)
    {
        $this->userService->assignRoles($user, $request->role_ids);
        $user->load('roles');

        return new UserResource($user);
    }

    /**
     * 同步用户角色（覆盖式）
     *
     * @urlParam user integer required 用户ID. Example: 1
     *
     * @bodyParam role_ids array required 角色ID数组（将完全替换用户当前的角色）. Example: [1, 2]
     *
     * @response {
     *   "id": 1,
     *   "name": "张三",
     *   "email": "<EMAIL>",
     *   "roles": [
     *     {
     *       "id": 1,
     *       "name": "管理员",
     *       "description": "系统管理员角色"
     *     },
     *     {
     *       "id": 2,
     *       "name": "普通用户",
     *       "description": "普通用户角色"
     *     }
     *   ]
     * }
     */
    public function sync(UserRoleSyncRequest $request, User $user)
    {
        $this->userService->syncRoles($user, $request->role_ids);
        $user->load('roles');

        return new UserResource($user);
    }

    /**
     * 移除用户角色
     *
     * @urlParam user integer required 用户ID. Example: 1
     *
     * @bodyParam role_ids array required 要移除的角色ID数组. Example: [1, 2]
     *
     * @response {
     *   "id": 1,
     *   "name": "张三",
     *   "email": "<EMAIL>",
     *   "roles": [
     *     {
     *       "id": 1,
     *       "name": "管理员",
     *       "description": "系统管理员角色"
     *     }
     *   ]
     * }
     */
    public function remove(UserRoleRemoveRequest $request, User $user)
    {
        $this->userService->removeRoles($user, $request->role_ids);
        $user->load('roles');

        return new UserResource($user);
    }
}
