<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class RoleMenuPermissionAssignRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'permissions' => 'required|array',
            'permissions.*.menu_id' => 'required|integer|exists:menus,id',
            'permissions.*.permission_ids' => 'required|array',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     */
    public function messages(): array
    {
        return [
            'permissions.required' => '权限数组不能为空',
            'permissions.array' => '权限必须是数组格式',
            'permissions.*.menu_id.required' => '菜单ID不能为空',
            'permissions.*.menu_id.integer' => '菜单ID必须是整数',
            'permissions.*.menu_id.exists' => '菜单不存在',
            'permissions.*.permission_ids.required' => '菜单权限=不能为空',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // 验证菜单权限ID是否属于对应的菜单
            $permissions = $this->input('permissions', []);

            foreach ($permissions as $index => $permission) {
                if (isset($permission['menu_permission_id']) && $permission['menu_permission_id']) {
                    $menuPermission = \App\Models\MenuPermission::find($permission['menu_permission_id']);

                    if ($menuPermission && $menuPermission->menu_id != $permission['menu_id']) {
                        $validator->errors()->add(
                            "permissions.{$index}.menu_permission_id",
                            '菜单权限不属于指定的菜单'
                        );
                    }
                }
            }
        });
    }
}
