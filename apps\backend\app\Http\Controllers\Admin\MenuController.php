<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\MenuRequest;
use App\Http\Resources\Admin\MenuResource;
use App\Models\Menu;
use App\Services\MenuService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Symfony\Component\HttpFoundation\Response;

/**
 * @group 菜单管理
 *
 * 系统菜单管理接口
 */
class MenuController extends Controller
{
    public function __construct(
        private MenuService $menuService
    ) {}

    /**
     * 获取菜单列表
     *
     * 获取当前用户有权限访问的菜单扁平数组，前端自行构建树形结构
     *
     * @response {
     *   "menuList": [
     *     {
     *       "id": 1,
     *       "parent_id": 0,
     *       "name": "User",
     *       "path": "/user",
     *       "component": "User",
     *       "title": "用户管理",
     *       "icon": "user",
     *       "label": "user",
     *       "sort": 1,
     *       "is_hide": false,
     *       "is_hide_tab": false,
     *       "link": "https://www.baidu.com",
     *       "is_iframe": false,
     *       "keep_alive": true,
     *       "is_first_level": false,
     *       "fixed_tab": false,
     *       "active_path": "/user",
     *       "is_full_page": false,
     *       "show_badge": false,
     *       "show_text_badge": "new",
     *       "status": true,
     *       "meta": {
     *         "title": "用户管理",
     *         "icon": "user",
     *         "keepAlive": true,
     *         "showBadge": false,
     *         "showTextBadge": "new",
     *         "isHide": false,
     *         "isHideTab": false,
     *         "link": "https://www.baidu.com",
     *         "isIframe": false,
     *         "authList": [
     *           {
     *             "title": "用户列表",
     *             "authMark": "user:list"
     *           }
     *         ],
     *         "isFirstLevel": false,
     *         "fixedTab": false,
     *         "activePath": "/user",
     *         "isFullPage": false
     *       }
     *     }
     *   ]
     * }
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();

        // 如果是管理员或者没有启用权限控制，返回所有菜单
        if ($user->hasRole('admin')) {
            $data = $this->menuService->getMenuList();
        } else {
            // 根据用户权限过滤菜单
            $data = $this->menuService->getMenuListByUser($user);
        }

        return response()->json($data);
    }

    /**
     * 获取菜单树
     *
     * 用于选择父级菜单
     *
     * @response {
     *   "data": [
     *     {
     *       "id": 1,
     *       "parent_id": 0,
     *       "name": "User",
     *       "path": "/user",
     *       "component": "User",
     *       "title": "用户管理",
     *       "icon": "user",
     *       "label": "user",
     *       "sort": 1,
     *       "is_hide": false,
     *       "is_hide_tab": false,
     *       "link": "https://www.baidu.com",
     *       "is_iframe": false,
     *       "keep_alive": true,
     *       "is_first_level": false,
     *       "fixed_tab": false,
     *       "active_path": "/user",
     *       "is_full_page": false,
     *       "show_badge": false,
     *       "show_text_badge": "new",
     *       "status": true,
     *       "meta": {
     *         "title": "用户管理",
     *         "icon": "user",
     *         "keepAlive": true,
     *         "showBadge": false,
     *         "showTextBadge": "new",
     *         "isHide": false,
     *         "isHideTab": false,
     *         "link": "https://www.baidu.com",
     *         "isIframe": false,
     *         "authList": [
     *           {
     *             "title": "用户列表",
     *             "authMark": "user:list"
     *           }
     *         ],
     *         "isFirstLevel": false,
     *         "fixedTab": false,
     *         "activePath": "/user",
     *         "isFullPage": false
     *       },
     *       "children": [
     *         {
     *           "id": 2,
     *           "parent_id": 1,
     *           "name": "UserList",
     *           "path": "/user/list",
     *           "component": "UserList",
     *           "title": "用户列表",
     *           "icon": "user",
     *           "label": "user:list",
     *           "sort": 1,
     *           "is_hide": false,
     *           "is_hide_tab": false,
     *           "link": "https://www.baidu.com",
     *           "is_iframe": false,
     *           "keep_alive": true,
     *           "is_first_level": false,
     *           "fixed_tab": false,
     *           "active_path": "/user/list",
     *           "is_full_page": false,
     *           "show_badge": false,
     *           "show_text_badge": "new",
     *           "status": true,
     *           "meta": {
     *             "title": "用户列表",
     *             "icon": "user",
     *             "keepAlive": true,
     *             "showBadge": false,
     *             "showTextBadge": "new",
     *             "isHide": false,
     *             "isHideTab": false,
     *             "link": "https://www.baidu.com",
     *             "isIframe": false,
     *             "authList": [
     *               {
     *                 "title": "用户列表",
     *                 "authMark": "user:list"
     *               }
     *             ],
     *             "isFirstLevel": false,
     *             "fixedTab": false,
     *             "activePath": "/user/list",
     *             "isFullPage": false
     *           }
     *         }
     *       ]
     *     }
     *   ]
     * }
     */
    public function tree(): AnonymousResourceCollection
    {
        $menus = $this->menuService->getMenuTree();

        return MenuResource::collection($menus);
    }

    /**
     * 创建菜单
     *
     * @bodyParam parent_id integer 父级菜单ID Example: 1
     * @bodyParam name string required 菜单名称 Example: 用户管理
     * @bodyParam path string required 菜单路径 Example: /user
     * @bodyParam component string 组件路径 Example: User
     * @bodyParam title string required 菜单标题 Example: 用户管理
     * @bodyParam icon string 菜单图标 Example: user
     * @bodyParam label string 权限标识 Example: user
     * @bodyParam sort integer 排序 Example: 1
     * @bodyParam is_hide boolean 是否隐藏 Example: false
     * @bodyParam is_hide_tab boolean 是否在标签页隐藏 Example: false
     * @bodyParam link string 外部链接 Example: https://www.baidu.com
     * @bodyParam is_iframe boolean 是否为iframe Example: false
     * @bodyParam keep_alive boolean 是否缓存 Example: true
     * @bodyParam is_first_level boolean 是否为一级菜单 Example: false
     * @bodyParam fixed_tab boolean 是否固定标签页 Example: false
     * @bodyParam active_path string 激活菜单路径 Example: /user
     * @bodyParam is_full_page boolean 是否全屏页面 Example: false
     * @bodyParam show_badge boolean 是否显示徽章 Example: false
     * @bodyParam show_text_badge string 文本徽章内容 Example: new
     * @bodyParam status boolean 状态 Example: true
     * @bodyParam permissions array 权限列表 Example: [{"title": "用户列表", "auth_mark": "user:list", "sort": 1}]
     * @bodyParam permissions.*.title string required 权限名称 Example: 用户列表
     * @bodyParam permissions.*.auth_mark string required 权限标识 Example: user:list
     * @bodyParam permissions.*.sort integer 排序 Example: 1
     *
     * @response {
     *   "data": {
     *     "id": 1,
     *     "parent_id": 0,
     *     "name": "User",
     *     "path": "/user",
     *     "component": "User",
     *     "title": "用户管理",
     *     "icon": "user",
     *     "label": "user",
     *     "sort": 1,
     *     "is_hide": false,
     *     "is_hide_tab": false,
     *     "link": "https://www.baidu.com",
     *     "is_iframe": false,
     *     "keep_alive": true,
     *     "is_first_level": false,
     *     "fixed_tab": false,
     *     "active_path": "/user",
     *     "is_full_page": false,
     *     "show_badge": false,
     *     "show_text_badge": "new",
     *     "status": true,
     *     "meta": {
     *       "title": "用户管理",
     *       "icon": "user",
     *       "keepAlive": true,
     *       "showBadge": false,
     *       "showTextBadge": "new",
     *       "isHide": false,
     *       "isHideTab": false,
     *       "link": "https://www.baidu.com",
     *       "isIframe": false,
     *       "authList": [
     *         {
     *           "title": "用户列表",
     *           "authMark": "user:list"
     *         }
     *       ],
     *       "isFirstLevel": false,
     *       "fixedTab": false,
     *       "activePath": "/user",
     *       "isFullPage": false
     *     }
     *   }
     * }
     */
    public function store(MenuRequest $request): MenuResource
    {
        $menu = $this->menuService->create($request->validated());

        return new MenuResource($menu);
    }

    /**
     * 更新菜单
     *
     * @bodyParam parent_id integer 父级菜单ID Example: 1
     * @bodyParam name string required 菜单名称 Example: 用户管理
     * @bodyParam path string required 菜单路径 Example: /user
     * @bodyParam component string 组件路径 Example: User
     * @bodyParam title string required 菜单标题 Example: 用户管理
     * @bodyParam icon string 菜单图标 Example: user
     * @bodyParam label string 权限标识 Example: user
     * @bodyParam sort integer 排序 Example: 1
     * @bodyParam is_hide boolean 是否隐藏 Example: false
     * @bodyParam is_hide_tab boolean 是否在标签页隐藏 Example: false
     * @bodyParam link string 外部链接 Example: https://www.baidu.com
     * @bodyParam is_iframe boolean 是否为iframe Example: false
     * @bodyParam keep_alive boolean 是否缓存 Example: true
     * @bodyParam is_first_level boolean 是否为一级菜单 Example: false
     * @bodyParam fixed_tab boolean 是否固定标签页 Example: false
     * @bodyParam active_path string 激活菜单路径 Example: /user
     * @bodyParam is_full_page boolean 是否全屏页面 Example: false
     * @bodyParam show_badge boolean 是否显示徽章 Example: false
     * @bodyParam show_text_badge string 文本徽章内容 Example: new
     * @bodyParam status boolean 状态 Example: true
     * @bodyParam permissions array 权限列表 Example: [{"title": "用户列表", "auth_mark": "user:list", "sort": 1}]
     * @bodyParam permissions.*.title string required 权限名称 Example: 用户列表
     * @bodyParam permissions.*.auth_mark string required 权限标识 Example: user:list
     * @bodyParam permissions.*.sort integer 排序 Example: 1
     *
     * @response {
     *   "data": {
     *     "id": 1,
     *     "parent_id": 0,
     *     "name": "User",
     *     "path": "/user",
     *     "component": "User",
     *     "title": "用户管理",
     *     "icon": "user",
     *     "label": "user",
     *     "sort": 1,
     *     "is_hide": false,
     *     "is_hide_tab": false,
     *     "link": "https://www.baidu.com",
     *     "is_iframe": false,
     *     "keep_alive": true,
     *     "is_first_level": false,
     *     "fixed_tab": false,
     *     "active_path": "/user",
     *     "is_full_page": false,
     *     "show_badge": false,
     *     "show_text_badge": "new",
     *     "status": true,
     *     "meta": {
     *       "title": "用户管理",
     *       "icon": "user",
     *       "keepAlive": true,
     *       "showBadge": false,
     *       "showTextBadge": "new",
     *       "isHide": false,
     *       "isHideTab": false,
     *       "link": "https://www.baidu.com",
     *       "isIframe": false,
     *       "authList": [
     *         {
     *           "title": "用户列表",
     *           "authMark": "user:list"
     *         }
     *       ],
     *       "isFirstLevel": false,
     *       "fixedTab": false,
     *       "activePath": "/user",
     *       "isFullPage": false
     *     }
     *   }
     * }
     */
    public function update(MenuRequest $request, Menu $menu): MenuResource
    {
        $menu = $this->menuService->update($menu, $request->validated());

        return new MenuResource($menu);
    }

    /**
     * 删除菜单
     *
     * @urlParam menu integer required 菜单ID Example: 1
     *
     * @response {
     *   "message": "菜单删除成功"
     * }
     */
    public function destroy(Menu $menu): Response
    {
        $this->menuService->delete($menu);

        return response()->noContent();
    }
}
