<?php

namespace App\Services;

use App\Models\Attachment;
use App\Models\AttachmentRelation;
use App\Services\Attachment\StorageManager;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class AttachmentService
{
    public function __construct(
        private StorageManager $storageManager
    ) {}

    /**
     * 上传文件（本地上传）
     */
    public function upload(UploadedFile $file, array $options = []): Attachment
    {
        return DB::transaction(function () use ($file, $options) {
            // 计算MD5
            $md5Hash = md5_file($file->getRealPath());

            // 检查秒传
            if ($existingAttachment = $this->checkQuickUpload($md5Hash)) {
                return $existingAttachment;
            }

            // 使用指定的驱动或默认驱动
            $driverName = $options['driver'] ?? config('attachment.default');
            $driver = $this->storageManager->driver($driverName);

            // 生成存储路径
            $path = $this->generateStoragePath($file);

            // 存储文件
            $storedPath = $driver->store($file, $path);

            // 创建附件记录
            return $this->createAttachment([
                'file_name' => $file->getClientOriginalName(),
                'file_path' => $storedPath,
                'file_size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'storage_type' => $driver->getType(),
                'md5_hash' => $md5Hash,
            ]);
        });
    }

    /**
     * 获取OSS直传签名
     */
    public function getPostSignature(array $params): array
    {
        // 检查秒传
        if (! empty($params['md5_hash']) && $existingAttachment = $this->checkQuickUpload($params['md5_hash'])) {
            return [
                'quick_upload' => true,
                'attachment' => $existingAttachment->toArray(),
            ];
        }

        // 获取OSS驱动
        $driver = $this->storageManager->driver('alioss');

        // 获取签名
        $signature = $driver->getPostSignature(
            $params['filename'],
            $params['filesize'],
            $params['mime_type'],
            $params['md5_hash'] ?? null
        );

        // 生成上传ID并缓存上传信息
        $uploadId = (string) Str::uuid();
        $cacheKey = "attachment:upload:{$uploadId}";
        $cacheData = array_merge($params, [
            'file_path' => $signature['file_path'],
            'storage_type' => 'alioss',
        ]);

        Cache::put($cacheKey, $cacheData, config('attachment.quick_upload.cache_ttl'));

        return array_merge($signature, [
            'quick_upload' => false,
            'upload_id' => $uploadId,
        ]);
    }

    /**
     * 处理OSS上传回调
     */
    public function handleOssCallback(array $data): Attachment
    {
        $uploadId = $data['upload_id'] ?? null;

        if (! $uploadId) {
            throw new \InvalidArgumentException('Missing upload_id');
        }

        // 获取缓存的上传信息
        $cacheKey = "attachment:upload:{$uploadId}";
        $cachedData = Cache::pull($cacheKey);

        if (! $cachedData) {
            throw new \InvalidArgumentException('Invalid or expired upload_id');
        }

        // 验证回调数据
        $driver = $this->storageManager->driver('alioss');
        if (! $driver->validateCallback($data)) {
            throw new \InvalidArgumentException('Invalid callback data');
        }

        // 创建附件记录
        return $this->createAttachment([
            'file_name' => $cachedData['filename'],
            'file_path' => $data['object_key'] ?? $cachedData['file_path'],
            'file_size' => $cachedData['filesize'],
            'mime_type' => $cachedData['mime_type'],
            'md5_hash' => $cachedData['md5_hash'] ?? null,
            'storage_type' => 'alioss',
        ]);
    }

    /**
     * 删除文件
     */
    public function delete(Attachment $attachment): bool
    {
        return DB::transaction(function () use ($attachment) {
            // 使用对应的驱动删除文件
            $driver = $this->storageManager->driver($attachment->storage_type);
            $driver->delete($attachment->file_path);

            // 软删除数据库记录
            return $attachment->delete();
        });
    }

    /**
     * 获取文件内容
     */
    public function getFileContent(Attachment $attachment): ?string
    {
        // 对于OSS文件，直接返回null，让前端通过URL访问
        if ($attachment->storage_type === 'alioss') {
            return null;
        }

        // 本地文件返回内容
        $driver = $this->storageManager->driver($attachment->storage_type);
        if ($driver->exists($attachment->file_path)) {
            return file_get_contents($driver->url($attachment->file_path));
        }

        return null;
    }

    /**
     * 检查秒传
     */
    private function checkQuickUpload(string $md5): ?Attachment
    {
        if (! config('attachment.quick_upload.enabled')) {
            return null;
        }

        return Attachment::where('md5_hash', $md5)->first();
    }

    /**
     * 创建附件记录
     */
    private function createAttachment(array $data): Attachment
    {
        return Attachment::create($data);
    }

    /**
     * 生成存储路径
     */
    protected function generateStoragePath(UploadedFile $file): string
    {
        $date = now()->format('Y/m/d');
        $fileName = Str::random(32).'.'.$file->getClientOriginalExtension();

        return "attachments/{$date}/{$fileName}";
    }

    /**
     * 分页查询
     */
    public function paginate(array $params = [], int $perPage = 20)
    {
        $query = Attachment::query();

        // 加载第一个关联（如果有的话）和创建人信息
        $query->with([
            'attachables' => function ($q) {
                $q->orderBy('created_at', 'desc')->limit(1);
            },
            'creator:id,name',
        ]);

        // 文件名搜索
        if (! empty($params['file_name'])) {
            $query->where('file_name', 'like', '%'.$params['file_name'].'%');
        }

        // 存储类型筛选
        if (! empty($params['storage_type'])) {
            $query->where('storage_type', $params['storage_type']);
        }

        // 时间范围
        if (! empty($params['start_time'])) {
            $query->where('created_at', '>=', $params['start_time']);
        }

        if (! empty($params['end_time'])) {
            $query->where('created_at', '<=', $params['end_time']);
        }

        // 通过关联表筛选
        if (! empty($params['attachable_type'])) {
            $query->whereHas('attachables', function ($q) use ($params) {
                $q->where('attachable_type', $params['attachable_type']);
            });
        }

        if (! empty($params['attachable_id'])) {
            $query->whereHas('attachables', function ($q) use ($params) {
                $q->where('attachable_id', $params['attachable_id']);
            });
        }

        if (! empty($params['category'])) {
            $query->whereHas('attachables', function ($q) use ($params) {
                $q->where('category', $params['category']);
            });
        }

        return $query->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    /**
     * 关联附件到业务实体
     */
    public function associate(Model $model, int $attachmentId, array $data = []): AttachmentRelation
    {
        Attachment::findOrFail($attachmentId);

        // 检查是否已关联
        $existingRelation = AttachmentRelation::where([
            'attachment_id' => $attachmentId,
            'attachable_id' => $model->id,
            'attachable_type' => get_class($model),
        ])->first();

        if ($existingRelation) {
            // 更新现有关联
            $existingRelation->update($data);

            return $existingRelation;
        }

        // 创建新关联
        return AttachmentRelation::create(array_merge($data, [
            'attachment_id' => $attachmentId,
            'attachable_id' => $model->id,
            'attachable_type' => get_class($model),
        ]));
    }

    /**
     * 批量关联附件
     */
    public function associateMany(Model $model, array $attachmentIds, array $data = []): Collection
    {
        $relations = collect();

        DB::transaction(function () use ($model, $attachmentIds, $data, &$relations) {
            foreach ($attachmentIds as $index => $attachmentId) {
                $relationData = array_merge($data, [
                    'sort' => $data['sort'] ?? $index,
                ]);

                $relations->push($this->associate($model, $attachmentId, $relationData));
            }
        });

        return $relations;
    }

    /**
     * 解除附件关联
     */
    public function dissociate(Model $model, int $attachmentId): bool
    {
        return AttachmentRelation::where([
            'attachment_id' => $attachmentId,
            'attachable_id' => $model->id,
            'attachable_type' => get_class($model),
        ])->delete() > 0;
    }

    /**
     * 解除模型的所有附件关联
     */
    public function dissociateAll(Model $model): int
    {
        return AttachmentRelation::where([
            'attachable_id' => $model->id,
            'attachable_type' => get_class($model),
        ])->delete();
    }

    /**
     * 获取业务实体的附件列表
     */
    public function getByModel(Model $model, ?string $category = null): Collection
    {
        $query = $model->attachments();

        if ($category) {
            $query->wherePivot('category', $category);
        }

        return $query->get();
    }

    /**
     * 同步附件关联（替换现有关联）
     */
    public function sync(Model $model, array $attachmentData): void
    {
        DB::transaction(function () use ($model, $attachmentData) {
            // 删除现有关联
            $this->dissociateAll($model);

            // 创建新关联
            foreach ($attachmentData as $data) {
                if (isset($data['attachment_id'])) {
                    $this->associate($model, $data['attachment_id'], $data);
                }
            }
        });
    }

    /**
     * 更新附件关联信息
     */
    public function updateRelation(int $relationId, array $data): AttachmentRelation
    {
        $relation = AttachmentRelation::findOrFail($relationId);

        $relation->update($data);

        return $relation;
    }
}
