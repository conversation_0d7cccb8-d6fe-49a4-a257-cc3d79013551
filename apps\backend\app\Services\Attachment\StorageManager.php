<?php

namespace App\Services\Attachment;

use App\Services\Attachment\Contracts\StorageDriver;
use App\Services\Attachment\Drivers\AliOssDriver;
use App\Services\Attachment\Drivers\LocalDriver;
use InvalidArgumentException;

class StorageManager
{
    protected array $drivers = [];

    /**
     * 获取存储驱动实例
     */
    public function driver(?string $name = null): StorageDriver
    {
        $name = $name ?: config('attachment.default');

        if (! isset($this->drivers[$name])) {
            $this->drivers[$name] = $this->createDriver($name);
        }

        return $this->drivers[$name];
    }

    /**
     * 创建存储驱动
     */
    protected function createDriver(string $name): StorageDriver
    {
        $config = config("attachment.drivers.{$name}");

        if (! $config) {
            throw new InvalidArgumentException("Driver [{$name}] configuration not found.");
        }

        return match ($name) {
            'local' => new LocalDriver($config),
            'alioss' => new AliOssDriver($config),
            default => throw new InvalidArgumentException("Driver [{$name}] not supported.")
        };
    }

    /**
     * 清除驱动缓存
     */
    public function forgetDriver(string $name): void
    {
        unset($this->drivers[$name]);
    }

    /**
     * 获取所有可用的驱动名称
     */
    public function getAvailableDrivers(): array
    {
        return array_keys(config('attachment.drivers', []));
    }
}
