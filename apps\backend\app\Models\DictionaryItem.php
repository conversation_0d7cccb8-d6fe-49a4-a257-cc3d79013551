<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class DictionaryItem extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'category_id',
        'code',
        'value',
        'label',
        'sort',
        'color',
        'icon',
        'config',
        'remark',
        'is_enabled',
        'created_by',
        'updated_by',
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'is_enabled' => 'boolean',
        'sort' => 'integer',
        'config' => 'array',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];

    /**
     * 隐藏的属性
     */
    protected $hidden = [
        'deleted_at',
    ];

    /**
     * 获取字典分类
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(DictionaryCategory::class, 'category_id');
    }

    /**
     * 获取显示文本
     */
    public function getDisplayTextAttribute(): string
    {
        return $this->label ?: $this->value;
    }

    /**
     * 作用域：启用状态
     */
    public function scopeEnabled($query)
    {
        return $query->where('is_enabled', true);
    }

    /**
     * 作用域：按排序字段排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort')->orderBy('id');
    }

    /**
     * 作用域：按分类查询
     */
    public function scopeOfCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * 作用域：按分类编码查询
     */
    public function scopeOfCategoryCode($query, $categoryCode)
    {
        return $query->whereHas('category', function ($q) use ($categoryCode) {
            $q->where('code', $categoryCode);
        });
    }
}
