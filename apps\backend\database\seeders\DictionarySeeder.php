<?php

namespace Database\Seeders;

use App\Models\DictionaryCategory;
use App\Models\DictionaryItem;
use Illuminate\Database\Seeder;

class DictionarySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => '统一布尔字典',
                'code' => 'yes_or_no',
                'description' => '是/否选项',
                'sort' => 1,
                'is_enabled' => true,
                'items' => [
                    ['label' => '是', 'code' => 'Y', 'value' => '是', 'color' => '#67C23A', 'sort' => 1, 'is_enabled' => true],
                    ['label' => '否', 'code' => 'N', 'value' => '否', 'color' => '#F56C6C', 'sort' => 2, 'is_enabled' => true],
                ],
            ],
            [
                'name' => '资产来源',
                'code' => 'asset_source',
                'description' => '设备资产来源类型',
                'sort' => 2,
                'is_enabled' => true,
                'items' => [
                    ['label' => '自产', 'code' => 'produce', 'value' => '自产', 'color' => '#409EFF', 'sort' => 1, 'is_enabled' => true],
                    ['label' => '采购', 'code' => 'purchase', 'value' => '采购', 'color' => '#E6A23C', 'sort' => 2, 'is_enabled' => true],
                    ['label' => '转让', 'code' => 'transfer', 'value' => '转让', 'color' => '#909399', 'sort' => 3, 'is_enabled' => true],
                    ['label' => '捐赠', 'code' => 'donate', 'value' => '捐赠', 'color' => '#67C23A', 'sort' => 4, 'is_enabled' => true],
                ],
            ],
            [
                'name' => '当前设备状态',
                'code' => 'asset_status',
                'description' => '设备当前状态',
                'sort' => 3,
                'is_enabled' => true,
                'items' => [
                    ['label' => '全新未出库', 'code' => 'new_unstocked', 'value' => '全新未出库', 'color' => '#67C23A', 'sort' => 1, 'is_enabled' => true],
                    ['label' => '正常使用中', 'code' => 'in_use', 'value' => '正常使用中', 'color' => '#409EFF', 'sort' => 2, 'is_enabled' => true],
                    ['label' => '待检测', 'code' => 'pending_check', 'value' => '待检测', 'color' => '#E6A23C', 'sort' => 3, 'is_enabled' => true],
                    ['label' => '报废登记', 'code' => 'scrap_registered', 'value' => '报废登记', 'color' => '#909399', 'sort' => 4, 'is_enabled' => true],
                    ['label' => '维修中', 'code' => 'under_repair', 'value' => '维修中', 'color' => '#F56C6C', 'sort' => 5, 'is_enabled' => true],
                ],
            ],
            [
                'name' => '成色',
                'code' => 'asset_condition',
                'description' => '设备成色状态',
                'sort' => 4,
                'is_enabled' => true,
                'items' => [
                    ['label' => '全新', 'code' => 'brand_new', 'value' => '全新', 'color' => '#67C23A', 'sort' => 1, 'is_enabled' => true],
                    ['label' => '二手', 'code' => 'second_hand', 'value' => '二手', 'color' => '#E6A23C', 'sort' => 2, 'is_enabled' => true],
                    ['label' => '翻新', 'code' => 'refurbished', 'value' => '翻新', 'color' => '#409EFF', 'sort' => 3, 'is_enabled' => true],
                ],
            ],
            [
                'name' => '质保期状态',
                'code' => 'warranty_status',
                'description' => '设备质保期状态',
                'sort' => 5,
                'is_enabled' => true,
                'items' => [
                    ['label' => '正常', 'code' => 'normal', 'value' => '正常', 'color' => '#67C23A', 'sort' => 1, 'is_enabled' => true],
                    ['label' => '过期', 'code' => 'expired', 'value' => '过期', 'color' => '#F56C6C', 'sort' => 2, 'is_enabled' => true],
                    ['label' => '续期', 'code' => 'renewed', 'value' => '续期', 'color' => '#409EFF', 'sort' => 3, 'is_enabled' => true],
                    ['label' => '无', 'code' => 'none', 'value' => '无', 'color' => '#909399', 'sort' => 4, 'is_enabled' => true],
                ],
            ],
            [
                'name' => '主体类型',
                'code' => 'entity_type',
                'description' => '主体类型分类',
                'sort' => 6,
                'is_enabled' => true,
                'items' => [
                    ['label' => '生产厂', 'code' => 'manufacturer', 'value' => '生产厂', 'color' => '#409EFF', 'sort' => 1, 'is_enabled' => true],
                    ['label' => '供应商', 'code' => 'supplier', 'value' => '供应商', 'color' => '#E6A23C', 'sort' => 2, 'is_enabled' => true],
                    ['label' => '最终客户', 'code' => 'end_customer', 'value' => '最终客户', 'color' => '#67C23A', 'sort' => 3, 'is_enabled' => true],
                    ['label' => '服务商', 'code' => 'service_provider', 'value' => '服务商', 'color' => '#909399', 'sort' => 4, 'is_enabled' => true],
                    ['label' => '售后部', 'code' => 'after_sales', 'value' => '售后部', 'color' => '#F56C6C', 'sort' => 5, 'is_enabled' => true],
                ],
            ],
            [
                'name' => '全生命周期配置选项',
                'code' => 'lifecycle_config',
                'description' => '生命周期管理配置项',
                'sort' => 7,
                'is_enabled' => true,
                'items' => [
                    ['label' => '安装', 'code' => 'installation', 'value' => '安装', 'color' => '#409EFF', 'sort' => 1, 'is_enabled' => true],
                    ['label' => '维护', 'code' => 'maintenance', 'value' => '维护', 'color' => '#67C23A', 'sort' => 2, 'is_enabled' => true],
                    ['label' => '培训', 'code' => 'training', 'value' => '培训', 'color' => '#E6A23C', 'sort' => 3, 'is_enabled' => true],
                    ['label' => '维修', 'code' => 'repair', 'value' => '维修', 'color' => '#F56C6C', 'sort' => 4, 'is_enabled' => true],
                    ['label' => '售后部', 'code' => 'after_sales_service', 'value' => '售后部', 'color' => '#909399', 'sort' => 5, 'is_enabled' => true],
                ],
            ],
            [
                'name' => '附件分类',
                'code' => 'attachment_category',
                'description' => '附件文件分类',
                'sort' => 8,
                'is_enabled' => true,
                'items' => [
                    ['label' => '通用附件', 'code' => 'general', 'value' => '通用附件', 'color' => '#409EFF', 'sort' => 1, 'is_enabled' => true],
                    ['label' => '用户头像', 'code' => 'avatar', 'value' => '用户头像', 'color' => '#67C23A', 'sort' => 2, 'is_enabled' => true],
                ],
            ],
            [
                'name' => '存储类型',
                'code' => 'storage_type',
                'description' => '文件存储类型',
                'sort' => 9,
                'is_enabled' => true,
                'items' => [
                    ['label' => '本地存储', 'code' => 'local', 'value' => '本地存储', 'sort' => 1, 'is_enabled' => true],
                    ['label' => '阿里云OSS', 'code' => 'alioss', 'value' => '阿里云OSS', 'sort' => 2, 'is_enabled' => true],
                    ['label' => '七牛云存储', 'code' => 'qiniu', 'value' => '七牛云存储', 'sort' => 3, 'is_enabled' => true],
                    ['label' => 'AWS S3', 'code' => 'aws', 'value' => 'AWS S3', 'sort' => 4, 'is_enabled' => true],
                ],
            ],
            [
                'name' => '附件业务类型',
                'code' => 'attachment_business_type',
                'description' => '附件可关联的业务模型类型',
                'sort' => 10,
                'is_enabled' => true,
                'items' => [
                    ['label' => '主体', 'code' => 'App\\Models\\Entity', 'value' => '主体', 'color' => '#409EFF', 'sort' => 1, 'is_enabled' => true],
                    ['label' => '生命周期', 'code' => 'App\\Models\\Lifecycle', 'value' => '生命周期', 'color' => '#67C23A', 'sort' => 2, 'is_enabled' => true],
                    ['label' => '生命周期跟进', 'code' => 'App\\Models\\LifecycleFollowUp', 'value' => '生命周期跟进', 'color' => '#E6A23C', 'sort' => 3, 'is_enabled' => true],
                    ['label' => '用户', 'code' => 'App\\Models\\User', 'value' => '用户', 'color' => '#909399', 'sort' => 4, 'is_enabled' => true],
                    ['label' => '资产', 'code' => 'App\\Models\\Asset', 'value' => '资产', 'color' => '#F56C6C', 'sort' => 5, 'is_enabled' => true],
                ],
            ],
        ];

        foreach ($categories as $categoryData) {
            $items = $categoryData['items'];
            unset($categoryData['items']);

            $category = DictionaryCategory::create($categoryData);

            foreach ($items as $itemData) {
                $itemData['category_id'] = $category->id;
                DictionaryItem::create($itemData);
            }
        }
    }
}
