<?php

namespace App\Services\Attachment\Drivers;

use App\Services\Attachment\Contracts\StorageDriver;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Str;

class AliOssDriver implements StorageDriver
{
    protected array $config;

    public function __construct(array $config)
    {
        $this->config = $config;
    }

    /**
     * 本地上传文件（OSS驱动不支持服务端上传）
     */
    public function store(UploadedFile $file, string $path): string
    {
        throw new \BadMethodCallException('AliOSS driver does not support server-side upload. Use PostObject instead.');
    }

    /**
     * 获取PostObject签名
     */
    public function getPostSignature(string $filename, int $filesize, string $mimeType, ?string $md5 = null): array
    {
        $dir = $this->config['prefix'].'/'.date('Y/m/d').'/';
        $key = $dir.Str::uuid().'.'.pathinfo($filename, PATHINFO_EXTENSION);

        // 构建Policy
        $expiration = date('c', time() + $this->config['policy_expire']);
        $conditions = [
            ['content-length-range', 0, $filesize],
            ['starts-with', '$key', $dir],
            ['eq', '$bucket', $this->config['bucket']],
        ];

        // 如果提供了MD5，加入到Policy中
        if ($md5) {
            $conditions[] = ['eq', '$x-oss-meta-md5', $md5];
        }

        $policy = [
            'expiration' => $expiration,
            'conditions' => $conditions,
        ];

        $policyBase64 = base64_encode(json_encode($policy));
        $signature = base64_encode(hash_hmac('sha1', $policyBase64, $this->config['access_key_secret'], true));

        // 构建上传URL
        $scheme = $this->config['use_ssl'] ? 'https' : 'http';
        $host = $this->config['is_cname']
            ? $this->config['endpoint']
            : $this->config['bucket'].'.'.$this->config['endpoint'];

        return [
            'upload_url' => $scheme.'://'.$host,
            'form_data' => [
                'key' => $key,
                'policy' => $policyBase64,
                'OSSAccessKeyId' => $this->config['access_key_id'],
                'signature' => $signature,
                'success_action_status' => '200',
                'x-oss-meta-md5' => $md5 ?: '',
            ],
            'file_path' => $key,
            'expires_at' => $expiration,
        ];
    }

    /**
     * 验证回调
     */
    public function validateCallback(array $data): bool
    {
        // 验证文件路径是否符合我们的规则
        if (! isset($data['object_key'])) {
            return false;
        }

        // 验证路径前缀
        $prefix = $this->config['prefix'];
        if (! str_starts_with($data['object_key'], $prefix)) {
            return false;
        }

        return true;
    }

    /**
     * 删除文件
     */
    public function delete(string $path): bool
    {
        // 这里需要使用OSS SDK或者REST API删除文件
        // 暂时返回true，实际项目中需要实现
        return true;
    }

    /**
     * 获取访问URL
     */
    public function url(string $path): string
    {
        $scheme = $this->config['use_ssl'] ? 'https' : 'http';
        $host = $this->config['is_cname']
            ? $this->config['endpoint']
            : $this->config['bucket'].'.'.$this->config['endpoint'];

        return $scheme.'://'.$host.'/'.ltrim($path, '/');
    }

    /**
     * 判断文件是否存在
     */
    public function exists(string $path): bool
    {
        // 需要使用OSS SDK或REST API检查
        // 暂时返回false
        return false;
    }

    /**
     * 获取存储类型标识
     */
    public function getType(): string
    {
        return 'alioss';
    }

    /**
     * 生成带签名的私有文件访问URL
     */
    public function getSignedUrl(string $path, int $expires = 3600): string
    {
        $resource = '/'.$this->config['bucket'].'/'.ltrim($path, '/');
        $expires = time() + $expires;

        $stringToSign = "GET\n\n\n{$expires}\n{$resource}";
        $signature = base64_encode(hash_hmac('sha1', $stringToSign, $this->config['access_key_secret'], true));

        $url = $this->url($path);
        $params = [
            'OSSAccessKeyId' => $this->config['access_key_id'],
            'Expires' => $expires,
            'Signature' => $signature,
        ];

        return $url.'?'.http_build_query($params);
    }
}
