<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class DynamicPermissionMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $request_path = $request->path();
        $path = preg_replace('/^api\//', '', $request_path); // 移除开头的 "api/"
        $method = $request->method();
        $admin = $request->user();

        // 获取所有权限
        $permissions = $admin->getAllPermissions();

        // 1. 首先尝试精确匹配
        $exactMatch = $permissions->where('path', $path)
            ->where('method', $method)
            ->first();

        // 如果精确匹配到权限，则放行
        if ($exactMatch) {
            return $next($request);
        }

        // 2. 检查动态路径匹配
        foreach ($permissions as $permission) {
            if ($permission->method !== $method) {
                continue;
            }

            // 将路径模式转换为正则表达式
            $pattern = preg_replace('/{[^}]+}/', '([^/]+)', $permission->path);
            $pattern = '#^'.$pattern.'$#';

            if (preg_match($pattern, $path)) {
                return $next($request);
            }
        }

        return response()->json(['message' => '没有权限'], 403);
    }
}
