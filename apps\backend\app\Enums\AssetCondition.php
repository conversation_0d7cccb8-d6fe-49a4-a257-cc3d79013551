<?php

namespace App\Enums;

/**
 * 成色
 * 
 * 此文件由 php artisan dictionary:generate-enums 命令自动生成
 * 请勿手动修改，如需更改请在字典管理中修改后重新生成
 * 
 * @generated 2025-07-24 12:49:04
 */
enum AssetCondition: string
{
    case BRAND_NEW = 'brand_new';
    case SECOND_HAND = 'second_hand';
    case REFURBISHED = 'refurbished';

    /**
     * 获取枚举对应的中文标签
     */
    public function label(): string
    {
        return match ($this) {
            self::BRAND_NEW => '全新',
            self::SECOND_HAND => '二手',
            self::REFURBISHED => '翻新',
        };
    }

    /**
     * 根据值获取枚举实例
     */
    public static function tryFromValue(string $value): ?self
    {
        return self::tryFrom($value);
    }

    /**
     * 检查值是否有效
     */
    public static function isValid(string $value): bool
    {
        return self::tryFrom($value) !== null;
    }
}
