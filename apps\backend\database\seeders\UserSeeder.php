<?php

namespace Database\Seeders;

use App\Models\Role;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 清空表数据 - 先禁用外键检查
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        User::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        // 只创建一个默认管理员
        $user = User::create([
            'username' => 'admin',
            'tenant_id' => 1,
            'password' => Hash::make('123456'),
            'email' => null,
            'phone' => null,
            'status' => 'enable',
        ]);

        // 分配管理员角色
        $adminRole = Role::where('name', 'admin')->first();
        if ($adminRole) {
            $user->roles()->attach($adminRole->id);
        }
    }
}
