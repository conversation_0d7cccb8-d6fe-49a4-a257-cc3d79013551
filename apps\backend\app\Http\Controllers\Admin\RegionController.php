<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\RegionSearchRequest;
use App\Models\Region;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

/**
 * @group 地区管理
 *
 * 省市区地区数据管理接口
 */
class RegionController extends Controller
{
    /**
     * 获取地区树形结构
     *
     * 获取完整的省市区三级树形结构数据，用于级联选择器
     *
     * @queryParam deep integer 获取的层级深度（可选，默认3级） Example: 3
     */
    public function tree(Request $request)
    {
        $deep = $request->input('deep', 3);

        $tree = Cache::remember("regions_tree_deep_{$deep}", 3600, function () {
            return Region::buildTree(0);
        });

        return response()->json($tree);
    }

    /**
     * 获取指定父级的子地区
     *
     * 用于懒加载获取子地区数据
     *
     * @urlParam parentId integer required 父级地区ID Example: 11
     */
    public function children($parentId)
    {
        $children = Cache::remember("regions_children_{$parentId}", 3600, function () use ($parentId) {
            return Region::where('pid', $parentId)
                ->select('id', 'pid', 'deep', 'name', 'ext_id', 'ext_name')
                ->orderBy('id')
                ->get()
                ->map(function ($region) {
                    return [
                        'value' => $region->ext_id,
                        'label' => $region->ext_name,
                        'isLeaf' => $region->deep >= 2, // 区县级为叶子节点
                        'id' => $region->id,
                        'deep' => $region->deep,
                    ];
                });
        });

        return response()->json($children);
    }

    /**
     * 根据地区代码获取完整路径
     *
     * 根据区县代码获取省市区完整路径信息
     *
     * @urlParam code string required 地区代码 Example: 110101000000
     */
    public function path($code)
    {
        $region = Region::findByCode($code);

        if (! $region) {
            return response()->json([
                'message' => '地区不存在',
            ], 404);
        }

        $path = Cache::remember("region_path_{$code}", 3600, function () use ($region) {
            return $region->getFullPath();
        });

        return response()->json([
            'path' => $path,
            'codes' => array_column($path, 'code'),
            'names' => array_column($path, 'name'),
            'full_name' => implode('', array_column($path, 'name')),
        ]);
    }

    /**
     * 搜索地区
     *
     * 根据关键词搜索地区，支持名称和拼音搜索
     *
     * @queryParam keyword string required 搜索关键词 Example: 北京
     * @queryParam limit integer 返回结果数量限制（默认20，最大50） Example: 20
     * @queryParam deep integer 搜索的层级（0省1市2区县，默认搜索所有层级） Example: 2
     */
    public function search(RegionSearchRequest $request)
    {

        $keyword = $request->input('keyword');
        $limit = $request->input('limit', 20);
        $deep = $request->input('deep');

        $query = Region::search($keyword);

        // 如果指定了层级，则只搜索该层级
        if ($deep !== null) {
            $query->where('deep', $deep);
        }

        $regions = $query->limit($limit)
            ->get()
            ->map(function ($region) {
                $fullPath = $region->getFullPath();

                return [
                    'value' => $region->ext_id,
                    'label' => $region->ext_name,
                    'path' => $fullPath,
                    'full_name' => implode('', array_column($fullPath, 'name')),
                    'deep' => $region->deep,
                    'pinyin' => $region->pinyin,
                ];
            });

        return response()->json($regions);
    }

    /**
     * 获取省份列表
     *
     * 获取所有省级行政区列表
     */
    public function provinces()
    {
        $provinces = Cache::remember('regions_provinces', 3600, function () {
            return Region::provinces()
                ->select('id', 'ext_id', 'ext_name', 'pinyin_prefix')
                ->orderBy('id')
                ->get()
                ->map(function ($region) {
                    return [
                        'value' => $region->ext_id,
                        'label' => $region->ext_name,
                        'id' => $region->id,
                        'pinyin_prefix' => $region->pinyin_prefix,
                    ];
                });
        });

        return response()->json($provinces);
    }

    /**
     * 获取指定省份的城市列表
     *
     * @urlParam provinceId integer required 省份ID Example: 11
     */
    public function cities($provinceId)
    {
        $cities = Cache::remember("regions_cities_{$provinceId}", 3600, function () use ($provinceId) {
            return Region::citiesOfProvince($provinceId)
                ->select('id', 'ext_id', 'ext_name', 'pinyin_prefix')
                ->orderBy('id')
                ->get()
                ->map(function ($region) {
                    return [
                        'value' => $region->ext_id,
                        'label' => $region->ext_name,
                        'id' => $region->id,
                        'pinyin_prefix' => $region->pinyin_prefix,
                    ];
                });
        });

        return response()->json($cities);
    }

    /**
     * 获取指定城市的区县列表
     *
     * @urlParam cityId integer required 城市ID Example: 1101
     */
    public function districts($cityId)
    {
        $districts = Cache::remember("regions_districts_{$cityId}", 3600, function () use ($cityId) {
            return Region::districtsOfCity($cityId)
                ->select('id', 'ext_id', 'ext_name', 'pinyin_prefix')
                ->orderBy('id')
                ->get()
                ->map(function ($region) {
                    return [
                        'value' => $region->ext_id,
                        'label' => $region->ext_name,
                        'id' => $region->id,
                        'pinyin_prefix' => $region->pinyin_prefix,
                    ];
                });
        });

        return response()->json($districts);
    }
}
