<?php

namespace App\Services;

use App\Models\Menu;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class MenuService
{
    /**
     * 获取菜单列表（返回扁平数组）
     * 参考 CategoryController 的实现方式
     */
    public function getMenuList(): array
    {
        // 获取扁平化的菜单数据，按照 parent_id 和 sort 排序
        $menus = Menu::with('permissions')
            ->where('status', true)
            ->orderBy('parent_id', 'asc')
            ->orderBy('sort', 'desc')  // 数字越大越靠前
            ->orderBy('id', 'desc')
            ->get();

        // 直接返回扁平数组，前端会自己构建树形结构
        return ['menuList' => $menus];
    }

    /**
     * 获取菜单树（用于选择父级菜单）
     */
    public function getMenuTree(): Collection
    {
        return Menu::select(['id', 'parent_id', 'title', 'name', 'path'])
            ->where('status', true)
            ->orderBy('parent_id', 'asc')
            ->orderBy('sort', 'desc')
            ->orderBy('id', 'desc')
            ->get();
    }

    /**
     * 根据用户权限获取菜单列表
     */
    public function getMenuListByUser(User $user): array
    {
        // 获取用户有权限访问的菜单
        $accessibleMenus = $user->getAccessibleMenus();

        // 获取菜单ID列表
        $menuIds = $accessibleMenus->pluck('id')->toArray();

        // 如果用户没有任何菜单权限，返回空数组
        if (empty($menuIds)) {
            return ['menuList' => []];
        }

        // 获取有权限的菜单及其父级菜单
        $allMenuIds = $this->getMenuIdsWithParents($menuIds);

        // 获取最终的菜单列表
        $menus = Menu::with('permissions')
            ->whereIn('id', $allMenuIds)
            ->where('status', true)
            ->orderBy('parent_id', 'asc')
            ->orderBy('sort', 'desc')
            ->orderBy('id', 'desc')
            ->get();

        // 过滤菜单权限，只显示用户有权限的操作
        $menus = $menus->map(function ($menu) use ($user) {
            // 获取用户对该菜单的权限
            $userPermissions = $user->getAllPermissions()
                ->where('menu_id', $menu->id)
                ->pluck('menu_permission_id')
                ->filter()
                ->toArray();

            // 过滤菜单的权限列表
            $menu->permissions = $menu->permissions->filter(function ($permission) use ($userPermissions) {
                return in_array($permission->id, $userPermissions);
            });

            return $menu;
        });

        return ['menuList' => $menus];
    }

    /**
     * 获取菜单ID及其所有父级菜单ID
     */
    private function getMenuIdsWithParents(array $menuIds): array
    {
        $allMenuIds = $menuIds;
        $parentIds = [];

        // 递归获取所有父级菜单ID
        $currentIds = $menuIds;
        while (!empty($currentIds)) {
            $parents = Menu::whereIn('id', $currentIds)
                ->whereNotNull('parent_id')
                ->pluck('parent_id')
                ->unique()
                ->toArray();

            $newParents = array_diff($parents, $allMenuIds);
            if (empty($newParents)) {
                break;
            }

            $allMenuIds = array_merge($allMenuIds, $newParents);
            $currentIds = $newParents;
        }

        return array_unique($allMenuIds);
    }

    /**
     * 创建菜单
     */
    public function create(array $data): Menu
    {
        return DB::transaction(function () use ($data) {
            // 提取权限数据
            $permissions = $data['permissions'] ?? [];
            unset($data['permissions']);

            // 创建菜单
            $menu = Menu::create($data);

            // 创建权限按钮
            foreach ($permissions as $permission) {
                $menu->permissions()->create($permission);
            }

            return $menu->fresh();
        });
    }

    /**
     * 更新菜单
     */
    public function update(Menu $menu, array $data): Menu
    {
        return DB::transaction(function () use ($menu, $data) {
            // 提取权限数据
            $permissions = $data['permissions'] ?? [];
            unset($data['permissions']);

            // 更新菜单
            $menu->update($data);

            // 更新权限按钮（先删除再创建）
            $menu->permissions()->delete();
            foreach ($permissions as $permission) {
                $menu->permissions()->create($permission);
            }

            return $menu->fresh();
        });
    }

    /**
     * 删除菜单
     */
    public function delete(Menu $menu): bool
    {
        return DB::transaction(function () use ($menu) {
            // 检查是否有子菜单
            $hasChildren = Menu::where('parent_id', $menu->id)->exists();
            if ($hasChildren) {
                throw new \Exception('该菜单下有子菜单，无法删除');
            }

            // 删除权限按钮
            $menu->permissions()->delete();

            // 删除菜单
            return $menu->delete();
        });
    }
}
