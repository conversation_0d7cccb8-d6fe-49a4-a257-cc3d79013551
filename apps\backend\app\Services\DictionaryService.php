<?php

namespace App\Services;

use App\Models\DictionaryCategory;
use App\Models\DictionaryItem;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class DictionaryService
{
    /**
     * 获取字典分类列表
     */
    public function getCategoryList(array $filters = []): Collection
    {
        return DictionaryCategory::query()
            ->when($filters['name'] ?? null, function (Builder $query, $value) {
                $query->where('name', 'like', "%{$value}%");
            })
            ->when($filters['code'] ?? null, function (Builder $query, $value) {
                $query->where('code', 'like', "%{$value}%");
            })
            ->when(isset($filters['is_enabled']), function (Builder $query) use ($filters) {
                $query->where('is_enabled', $filters['is_enabled']);
            })
            ->withCount('items')
            ->orderBy('sort')
            ->orderBy('id', 'desc')
            ->get();
    }

    /**
     * 获取字典项列表
     */
    public function getItemList(array $filters = []): Collection
    {
        return DictionaryItem::query()
            ->when($filters['category_id'] ?? null, function (Builder $query, $value) {
                $query->where('category_id', $value);
            })
            ->when($filters['code'] ?? null, function (Builder $query, $value) {
                $query->where('code', 'like', "%{$value}%");
            })
            ->when($filters['value'] ?? null, function (Builder $query, $value) {
                $query->where('value', 'like', "%{$value}%");
            })
            ->when(isset($filters['is_enabled']), function (Builder $query) use ($filters) {
                $query->where('is_enabled', $filters['is_enabled']);
            })
            ->with('category:id,name,code')
            ->orderBy('sort')
            ->orderBy('id', 'desc')
            ->get();
    }

    /**
     * 创建字典分类
     */
    public function createCategory(array $data): DictionaryCategory
    {
        return DB::transaction(function () use ($data) {
            $category = DictionaryCategory::create($data);

            return $category;
        });
    }

    /**
     * 更新字典分类
     */
    public function updateCategory(DictionaryCategory $category, array $data): DictionaryCategory
    {
        return DB::transaction(function () use ($category, $data) {
            $category->update($data);

            return $category->fresh();
        });
    }

    /**
     * 删除字典分类
     */
    public function deleteCategory(DictionaryCategory $category): bool
    {
        return DB::transaction(function () use ($category) {
            // 删除分类（会级联删除字典项）
            $result = $category->delete();

            return $result;
        });
    }

    /**
     * 创建字典项
     */
    public function createItem(array $data): DictionaryItem
    {
        return DB::transaction(function () use ($data) {
            $item = DictionaryItem::create($data);

            return $item->load('category');
        });
    }

    /**
     * 更新字典项
     */
    public function updateItem(DictionaryItem $item, array $data): DictionaryItem
    {
        return DB::transaction(function () use ($item, $data) {
            $item->update($data);

            return $item->fresh('category');
        });
    }

    /**
     * 删除字典项
     */
    public function deleteItem(DictionaryItem $item): bool
    {
        return DB::transaction(function () use ($item) {
            $categoryCode = $item->category->code;
            $result = $item->delete();

            return $result;
        });
    }

    /**
     * 根据分类编码获取字典项
     */
    public function getItemsByCode(string $categoryCode): array
    {
        $category = DictionaryCategory::where('code', $categoryCode)
            ->where('is_enabled', true)
            ->first();

        if (! $category) {
            return [];
        }

        return DictionaryItem::where('category_id', $category->id)
            ->where('is_enabled', true)
            ->orderBy('sort')
            ->get()
            ->toArray();
    }
}
