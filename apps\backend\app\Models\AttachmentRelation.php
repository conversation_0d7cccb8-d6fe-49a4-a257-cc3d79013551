<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class AttachmentRelation extends Model
{
    use HasFactory;

    protected $fillable = [
        'attachment_id',
        'attachable_id',
        'attachable_type',
        'category',
        'sort',
        'description',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'sort' => 'integer',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];

    /**
     * 获取附件
     */
    public function attachment(): BelongsTo
    {
        return $this->belongsTo(Attachment::class);
    }

    /**
     * 获取关联的业务实体
     */
    public function attachable(): MorphTo
    {
        return $this->morphTo();
    }
}
