<?php

namespace App\Enums;

/**
 * 维护周期单位
 *
 * 此文件由 php artisan dictionary:generate-enums 命令自动生成
 * 请勿手动修改，如需更改请在字典管理中修改后重新生成
 *
 * @generated 2025-07-14 15:18:15
 */
enum MaintenanceCycleUnit: string
{
    case DAY = 'day';
    case MONTH = 'month';

    /**
     * 获取枚举对应的中文标签
     */
    public function label(): string
    {
        return match ($this) {
            self::DAY => '天',
            self::MONTH => '月',
        };
    }

    /**
     * 根据值获取枚举实例
     */
    public static function tryFromValue(string $value): ?self
    {
        return self::tryFrom($value);
    }

    /**
     * 检查值是否有效
     */
    public static function isValid(string $value): bool
    {
        return self::tryFrom($value) !== null;
    }
}
